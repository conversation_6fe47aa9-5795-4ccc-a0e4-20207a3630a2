package com.avatar.hospital.chaperone.database.part.dataobject;

import com.avatar.hospital.chaperone.database.part.dataobject.base.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 备件入库审批单关联备件批次
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_spare_part_stock_apply_ref_part_batch")
public class PartStockApplyRefPartBatchDO extends TenantBaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 备件批次ID
     */
    private Long sparePartBatchId;

    /**
     * 入库审批单ID
     */
    private Long stockApplyId;

    /**
     * 数量
     */
    private Integer quantity;
}

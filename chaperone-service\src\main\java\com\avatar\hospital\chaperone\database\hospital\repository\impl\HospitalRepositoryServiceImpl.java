package com.avatar.hospital.chaperone.database.hospital.repository.impl;

import com.avatar.hospital.chaperone.database.hospital.dataobject.HospitalDO;
import com.avatar.hospital.chaperone.database.hospital.mapper.HospitalMapper;
import com.avatar.hospital.chaperone.database.hospital.repository.HospitalRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.hospital.HospitalPagingRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.DefaultUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 院区管理服务实现
 */
@Service
public class HospitalRepositoryServiceImpl extends ServiceImpl<HospitalMapper, HospitalDO> implements HospitalRepositoryService {

    @Override
    public long add(HospitalDO hospitalDO) {
        if (hospitalDO == null) {
            return 0;
        }

        hospitalDO.setId(IdUtils.getId());
        if (!save(hospitalDO)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
        return hospitalDO.getId();
    }

    @Override
    public boolean update(HospitalDO hospitalDO) {
        return false;
    }


    @Override
    public HospitalDO findById(Long id) {
        if (id == null || id < 0) {
            return null;
        }
        LambdaQueryWrapper<HospitalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HospitalDO::getId, id);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public PageResponse<HospitalDO> paging(HospitalPagingRequest request) {
        int index = DefaultUtils.ifNullDefault(request.getPageIndex(), 1);
        int size = DefaultUtils.ifNullDefault(request.getPageSize(), 10);

        LambdaQueryWrapper<HospitalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(request.getName() != null, HospitalDO::getName, request.getName());
        Page<HospitalDO> page = baseMapper.selectPage(new Page<>(index, size), queryWrapper);
        return PageResponse.build(page.getTotal(), page.getCurrent(), page.getSize(), page.getRecords());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(Set<Long> ids, Long updateBy) {
        if (CollectionUtils.isEmpty(ids) || updateBy == null) {
            return true;
        }
        LambdaUpdateWrapper<HospitalDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(HospitalDO::getDeleted, System.currentTimeMillis());
        wrapper.set(HospitalDO::getUpdateBy, updateBy);
        if (ids.size() == 1) {

        }

        return null;

    }

    @Override
    public List<HospitalDO> getResourceData() {
        LambdaQueryWrapper<HospitalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HospitalDO::getEnable, 1);
        queryWrapper.eq(HospitalDO::getDeleted, 0);
        return baseMapper.selectList(queryWrapper);
    }
}

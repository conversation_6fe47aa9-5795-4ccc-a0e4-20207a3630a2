package com.avatar.hospital.chaperone.web;

import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.annotation.MapperScans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/7
 */
@ComponentScan("com.avatar.hospital.chaperone")
@MapperScans(value = {
        @MapperScan(value = "com.avatar.hospital.chaperone.database.baccount.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.caccount.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.item.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.message.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.nursing.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.order.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.statistics.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.*.mapper"),

})
@SpringBootApplication
@EnableScheduling   // 启用定时任务
public class HospitalChaperoneWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(HospitalChaperoneWebApplication.class, args);
    }

}

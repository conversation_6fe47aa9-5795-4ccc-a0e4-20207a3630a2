package com.avatar.hospital.chaperone.web.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import com.avatar.hospital.chaperone.context.UserContext;
import com.avatar.hospital.chaperone.web.utils.CurrentUserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 统一的用户上下文拦截器
 * 功能1: 自动初始化用户信息（包含院区ID）
 * 功能2: 请求结束后自动清理，防止内存泄漏
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserContextInterceptor implements HandlerInterceptor {

    private final CurrentUserUtils currentUserUtils;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // 跳过不需要处理的请求
            if (shouldSkip(request)) {
                return true;
            }

            // 如果用户已登录，初始化用户上下文（包含院区信息）
            if (StpUtil.isLogin()) {
                currentUserUtils.initUserContext();
                log.debug("初始化用户上下文: uri={}, userId={}, hospitalId={}",
                         request.getRequestURI(), UserContext.currentUserId(), UserContext.currentHospitalId());
            }

            return true;
        } catch (Exception e) {
            log.error("用户上下文拦截器处理失败: uri={}", request.getRequestURI(), e);
            return true; // 不阻断请求
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                              Object handler, Exception ex) throws Exception {
        // 请求结束后清理ThreadLocal缓存，防止内存泄漏
        try {
            if (UserContext.exists()) {
                log.debug("清理用户上下文: uri={}", request.getRequestURI());
                UserContext.clear();
            }
        } catch (Exception e) {
            log.error("清理用户上下文失败", e);
        }
    }

    /**
     * 判断是否跳过处理
     */
    private boolean shouldSkip(HttpServletRequest request) {
        String uri = request.getRequestURI();

        // 跳过登录相关接口
        if (uri.startsWith("/api/v1/web/auth/") || uri.startsWith("/api/v1/web/login")) {
            return true;
        }

        // 跳过健康检查接口
        if (uri.startsWith("/actuator/")) {
            return true;
        }

        // 跳过静态资源
        if (uri.startsWith("/static/") || uri.startsWith("/public/")) {
            return true;
        }

        return false;
    }
}
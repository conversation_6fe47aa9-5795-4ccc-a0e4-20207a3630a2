package com.avatar.hospital.chaperone.tenant.handler;

import com.avatar.hospital.chaperone.context.UserContext;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 租户处理器 - 核心功能：自动添加 where hid = ?
 * 专注于两个核心功能：
 * 1. 查询时自动添加 where hid = ? 条件
 * 2. 配置哪些表需要租户隔离
 */
@Slf4j
@Component
public class TenantHandler implements TenantLineHandler {

    /**
     * 需要租户隔离的表
     */
    private static final List<String> TENANT_TABLES = Arrays.asList(
            // B端和C端用户表
            "t_b_account", "t_c_account", "t_organization",

            // 业务核心表
            "t_order", "t_nursing", "t_item", "t_message",

            // 项目相关表
            "t_project_patrol_plan", "t_project_maintenance_plan",
            // 设备
            "t_project_device", "t_project_spare_part",

            // 备件
            "t_project_spare_part_batch", "t_project_spare_part_stock_apply",
            "t_project_spare_part_stock_apply_ref_part_batch", "t_project_spare_part_apply", "t_project_spare_part_apply_ref_part_batch",
            // 报修
            "t_project_repair_form", "t_project_repair_form_feedback", "t_project_repair_form_task",

            // 关联表
            "t_nursing_hospital",

            // 统计表
            "t_statistics_order");

    /**
     * 全局共享表（不需要租户隔离）
     */
    private static final List<String> GLOBAL_TABLES = Arrays.asList(
            // 菜单
            "t_menu",
            // 权限表
            "t_role", "t_role_menu", "t_account_role", "t_b_account",
            // 院区
            "hospital",
            // 系统表
            "t_scheduled_log", "worker_node", "t_account_open_id", "t_project_code");

    /**
     * 租户字段名 - 使用hid字段
     */
    @Override
    public String getTenantIdColumn() {
        return "hid";
    }

    /**
     * 核心功能1: 自动添加 where hid = ? 条件
     * 从UserContext获取当前用户的院区ID
     */
    @Override
    public Expression getTenantId() {
        Long hospitalId = UserContext.currentHospitalId();
        if (hospitalId == null) {
            log.debug("院区ID为空，返回-1确保查询不到数据");
            return new LongValue(-1);
        }
        log.debug("使用院区ID进行数据隔离: {}", hospitalId);
        return new LongValue(hospitalId);
    }

    /**
     * 判断表是否需要租户隔离
     * 返回true表示忽略（不进行租户隔离）
     * 返回false表示需要进行租户隔离
     */
    @Override
    public boolean ignoreTable(String tableName) {
        // 全局共享表不进行租户隔离
        if (GLOBAL_TABLES.contains(tableName)) {
            log.debug("表 {} 是全局共享表，跳过租户隔离", tableName);
            return true;
        }

        // 检查是否需要租户隔离
        boolean needTenantFilter = TENANT_TABLES.contains(tableName);
        if (needTenantFilter) {
            log.debug("表 {} 需要租户隔离", tableName);
        } else {
            log.debug("表 {} 不在租户隔离列表中，跳过", tableName);
        }

        return !needTenantFilter;
    }
}

package com.avatar.hospital.chaperone.database.repair.dataobject;

import com.avatar.hospital.chaperone.database.part.dataobject.base.TenantBaseDO;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormBizType;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormSourceType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 报修单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_repair_form")
public class RepairFormDO extends TenantBaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 编号
     */
    private String code;

    /**
     * 状态（1-未指派，2-已指派，3-未完成，4-待审核，5-已完成）
     * @see com.avatar.hospital.chaperone.database.repair.enums.RepairFormStatus
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 报修系统类型（1-水系统,2-强电系统,3-弱电系统,4-气路系统,5-工程质量投诉,6-其他,7-暖通系统，8-建筑系统，9-消防系统）
     */
    private Integer systemType;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 附件URL,数组
     */
    private String attachments;

    /**
     * 来源（1-pc,2-h5）
     * @see RepairFormSourceType
     */
    private Integer sourceType;

    /**
     * 关联业务ID
     */
    private Long bizId;

    /**
     * 关联业务（0-未关联 1-巡检计划任务，2-维保计划任务）
     * @see RepairFormBizType
     */
    private Integer bizType;

    /**
     * 完成时间
     */
    private LocalDateTime completedTime;

    /**
     * 处理时间 秒
     */
    private Long processTime;

    /**
     * 派工人员ID
     */
    private Long assignerAccountId;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 紧急程度
     *
     * @see com.avatar.hospital.chaperone.database.repair.enums.RepairFormUrgencyDegreeType
     */
    private Integer urgencyDegreeType;


    /**
     * 报修位置
     */
    private String location;

}

package com.avatar.hospital.chaperone.database.hospital.repository;


import com.avatar.hospital.chaperone.database.hospital.dataobject.HospitalDO;
import com.avatar.hospital.chaperone.request.hospital.HospitalPagingRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * 院区信息数据访问层
 */

public interface HospitalRepositoryService extends IService<HospitalDO> {

    long add(HospitalDO hospitalDO);

    boolean update(HospitalDO hospitalDO);

    HospitalDO findById(Long id);

    // 分页
    PageResponse<HospitalDO> paging(HospitalPagingRequest request);

    // 删除
    Boolean deleteByIds(Set<Long> ids, Long updateBy);

    List<HospitalDO> getResourceData();
}

# Chaperone Admin Module

超级管理员模块 - 独立于租户系统，用于管理所有租户的数据和系统配置。

## 🎯 模块特点

### 独立性
- **完全独立于租户系统**：不受租户隔离插件影响，可以访问所有租户的数据
- **独立的用户体系**：拥有独立的管理员用户表和认证体系
- **独立的端口**：运行在8081端口，与主应用分离

### 功能特性
- **跨租户数据管理**：可以查看和管理所有租户的数据
- **租户统计分析**：提供详细的租户使用情况统计
- **系统监控**：监控整个系统的运行状态
- **数据导出**：支持租户数据的导出功能
- **操作审计**：记录所有管理员操作日志

## 🏗️ 技术架构

### 核心技术栈
- **Spring Boot 2.7.6**：基础框架
- **MyBatis Plus 3.5.2**：数据访问层（不启用租户插件）
- **SA-Token**：认证授权框架
- **MySQL 8.0**：数据库
- **Redis**：缓存和会话存储

### 关键设计
- **不使用租户插件**：MyBatis Plus配置中不添加租户插件，确保可以访问所有数据
- **独立的数据库表**：使用独立的管理员用户表，不与业务用户表混合
- **角色权限控制**：基于SA-Token的角色权限控制

## 📁 项目结构

```
chaperone-admin/
├── src/main/java/com/avatar/hospital/chaperone/admin/
│   ├── AdminApplication.java              # 启动类
│   ├── common/                           # 通用类
│   │   └── Result.java                   # 统一响应结果
│   ├── config/                           # 配置类
│   │   ├── GlobalExceptionHandler.java   # 全局异常处理
│   │   ├── MybatisPlusConfig.java        # MyBatis Plus配置（不启用租户插件）
│   │   └── SaTokenConfig.java            # SA-Token配置
│   ├── controller/                       # 控制器
│   │   ├── AuthController.java           # 认证控制器
│   │   └── TenantManageController.java   # 租户管理控制器
│   ├── dto/                             # 数据传输对象
│   │   ├── AdminUserDTO.java            # 管理员用户DTO
│   │   └── LoginRequest.java            # 登录请求DTO
│   ├── entity/                          # 实体类
│   │   └── AdminUser.java               # 管理员用户实体
│   ├── mapper/                          # 数据访问层
│   │   └── AdminUserMapper.java         # 管理员用户Mapper
│   ├── service/                         # 服务层
│   │   ├── AdminUserService.java        # 管理员用户服务接口
│   │   ├── TenantManageService.java     # 租户管理服务接口
│   │   └── impl/                        # 服务实现
│   │       └── AdminUserServiceImpl.java # 管理员用户服务实现
│   └── vo/                              # 视图对象
│       ├── LoginResponse.java           # 登录响应VO
│       ├── TenantInfo.java             # 租户信息VO
│       └── TenantStatistics.java       # 租户统计VO
├── src/main/resources/
│   ├── application.yaml                 # 主配置文件
│   ├── application-dev.yaml            # 开发环境配置
│   └── sql/
│       └── admin_init.sql              # 数据库初始化脚本
└── README.md                           # 说明文档
```

## 🚀 快速开始

### 1. 数据库初始化

执行初始化脚本：
```bash
mysql -u root -p chaperone < src/main/resources/sql/admin_init.sql
```

### 2. 配置文件

修改 `application-dev.yaml` 中的数据库和Redis配置：
```yaml
db:
  mysql: localhost
  username: root
  password: 123456

redis:
  host: localhost
  port: 6379
  password: 
  database: 2
```

### 3. 启动应用

```bash
mvn spring-boot:run
```

应用将在 `http://localhost:8081/admin` 启动

### 4. 默认账号

- **超级管理员**：
  - 用户名：`admin`
  - 密码：`admin123`
  - 权限：全部权限

- **系统管理员**：
  - 用户名：`system`
  - 密码：`admin123`
  - 权限：受限权限

## 📚 API文档

### 认证接口

#### 登录
```http
POST /admin/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "admin123"
}
```

#### 获取用户信息
```http
GET /admin/auth/userinfo
Authorization: Bearer {token}
```

#### 登出
```http
POST /admin/auth/logout
Authorization: Bearer {token}
```

### 租户管理接口

#### 获取租户列表
```http
GET /admin/tenant/list?current=1&size=10&keyword=
Authorization: Bearer {token}
```

#### 获取租户详情
```http
GET /admin/tenant/{tenantId}
Authorization: Bearer {token}
```

#### 获取租户统计
```http
GET /admin/tenant/{tenantId}/statistics
Authorization: Bearer {token}
```

#### 获取所有租户统计概览
```http
GET /admin/tenant/statistics/overview
Authorization: Bearer {token}
```

## 🔐 权限控制

### 角色定义
- **SUPER_ADMIN**：超级管理员，拥有所有权限
- **SYSTEM_ADMIN**：系统管理员，拥有受限权限

### 权限范围
- **ALL**：全部权限，可以执行所有操作
- **LIMITED**：受限权限，不能执行危险操作（如数据重置）

### 接口权限
- 所有接口都需要登录认证
- 部分危险操作需要超级管理员权限
- 使用SA-Token的注解进行权限控制

## 🔧 配置说明

### 端口配置
- 默认端口：8081
- 上下文路径：/admin

### 数据库配置
- 使用独立的管理员用户表
- 不启用MyBatis Plus的租户插件
- 支持逻辑删除

### 缓存配置
- 使用Redis存储会话信息
- 默认使用database 2，避免与主应用冲突

## 🚨 安全注意事项

### 密码安全
- 密码使用MD5+盐值加密
- 建议定期更换默认密码

### 网络安全
- 建议在生产环境中限制访问IP
- 使用HTTPS协议

### 操作审计
- 所有操作都会记录日志
- 包含操作人、操作时间、操作内容等信息

## 📊 监控和日志

### 应用监控
- 支持Spring Boot Actuator
- 提供健康检查接口

### 日志配置
- 独立的日志文件：`logs/admin/admin.log`
- 支持按日期滚动
- 记录详细的操作日志

## 🔄 部署建议

### 开发环境
- 可以与主应用在同一服务器运行
- 使用不同的端口区分

### 生产环境
- 建议部署在独立的服务器
- 配置防火墙限制访问
- 定期备份管理员数据

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础的管理员认证功能
- 实现租户数据查看功能
- 实现基础的统计功能

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

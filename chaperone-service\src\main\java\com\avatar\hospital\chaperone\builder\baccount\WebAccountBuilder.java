package com.avatar.hospital.chaperone.builder.baccount;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleDO;
import com.avatar.hospital.chaperone.request.baccount.WebAccountAddRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountPagingRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountBasicResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountPagingResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/10
 */
public class WebAccountBuilder {

    public static AccountDO buildAccountDO(WebAccountAddRequest request) {
        if (request == null) {
            return null;
        }
        AccountDO accountDO = new AccountDO();
        accountDO.setNickname(request.getNickname());
        accountDO.setPhoneNumber(request.getPhoneNumber());
        accountDO.setAvatarUrl(request.getAvatarUrl());
        accountDO.setPassword(request.getPassword());
        accountDO.setStatus(request.getStatus());
        accountDO.setType(request.getType());
        accountDO.setAdmin(Boolean.FALSE);
        accountDO.setHid(request.getHospitalId());
        accountDO.setCreateBy(request.getCreateBy());
        accountDO.setUpdateBy(request.getUpdateBy());
        return accountDO;
    }

    public static AccountDO buildAccountDO(WebAccountUpdateRequest request) {
        if (request == null) {
            return null;
        }
        AccountDO accountDO = new AccountDO();
        accountDO.setId(request.getId());
        accountDO.setNickname(request.getNickname());
        accountDO.setPhoneNumber(request.getPhoneNumber());
        accountDO.setAvatarUrl(request.getAvatarUrl());
        accountDO.setStatus(request.getStatus());
        accountDO.setType(request.getType());
        accountDO.setUpdateBy(request.getUpdateBy());
        return accountDO;
    }

    public static AccountDO buildAccountDO(WebAccountPagingRequest request) {
        if (request == null) {
            return null;
        }
        AccountDO accountDO = new AccountDO();
        accountDO.setPhoneNumber(request.getPhoneNumber());
        accountDO.setNickname(request.getNickname());
        return accountDO;
    }

    public static PageResponse<WebAccountPagingResponse> buildAccountPagingResponse(PageResponse<AccountDO> pageResponse) {
        if (pageResponse == null) {
            return null;
        }
        PageResponse<WebAccountPagingResponse> response = new PageResponse<>();
        response.setTotal(pageResponse.getTotal());
        response.setCurrent(pageResponse.getCurrent());
        response.setSize(pageResponse.getSize());
        response.setRecords(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(pageResponse.getRecords())) {
            response.setRecords(buildAccountPagingResponse(pageResponse.getRecords()));
        }
        return response;
    }

    public static List<WebAccountPagingResponse> buildAccountPagingResponse(List<AccountDO> accountList) {
        if (CollectionUtils.isEmpty(accountList)) {
            return Collections.emptyList();
        }
        List<WebAccountPagingResponse> webAccountPagingResponse = Lists.newLinkedList();
        for (AccountDO accountDO : accountList) {
            webAccountPagingResponse.add(buildAccountPagingResponse(accountDO));
        }
        return webAccountPagingResponse;
    }

    public static WebAccountPagingResponse buildAccountPagingResponse(AccountDO accountDO) {
        if (accountDO == null) {
            return null;
        }
        WebAccountPagingResponse webAccountPagingResponse = new WebAccountPagingResponse();
        webAccountPagingResponse.setId(accountDO.getId());
        webAccountPagingResponse.setHospitalId(accountDO.getHid());
        webAccountPagingResponse.setNickname(accountDO.getNickname());
        webAccountPagingResponse.setPhoneNumber(accountDO.getPhoneNumber());
        webAccountPagingResponse.setAvatarUrl(accountDO.getAvatarUrl());
        webAccountPagingResponse.setStatus(accountDO.getStatus());
        webAccountPagingResponse.setType(accountDO.getType());
        webAccountPagingResponse.setLastLoginIp(accountDO.getLastLoginIp());
        webAccountPagingResponse.setLastLoginDate(accountDO.getLastLoginDate());
        webAccountPagingResponse.setPasswordUpdateDate(accountDO.getPasswordUpdateDate());
        webAccountPagingResponse.setCreateBy(accountDO.getCreateBy());
        webAccountPagingResponse.setUpdateBy(accountDO.getUpdateBy());
        webAccountPagingResponse.setCreatedAt(accountDO.getCreatedAt());
        webAccountPagingResponse.setUpdatedAt(accountDO.getUpdatedAt());
        return webAccountPagingResponse;

    }

    public static WebAccountDetailResponse buildAccountDetailResponse(AccountDO accountDO, List<OrganizationDO> departmentDOList, List<RoleDO> roleDOList) {
        if (accountDO == null) {
            return null;
        }
        WebAccountDetailResponse webAccountDetailResponse = new WebAccountDetailResponse();
        webAccountDetailResponse.setId(accountDO.getId());
        webAccountDetailResponse.setHospitalId(accountDO.getHid());
        webAccountDetailResponse.setNickname(accountDO.getNickname());
        webAccountDetailResponse.setPhoneNumber(accountDO.getPhoneNumber());
        webAccountDetailResponse.setAvatarUrl(accountDO.getAvatarUrl());
        webAccountDetailResponse.setStatus(accountDO.getStatus());
        webAccountDetailResponse.setType(accountDO.getType());
        webAccountDetailResponse.setLastLoginIp(accountDO.getLastLoginIp());
        webAccountDetailResponse.setLastLoginDate(accountDO.getLastLoginDate());
        webAccountDetailResponse.setPasswordUpdateDate(accountDO.getPasswordUpdateDate());
        webAccountDetailResponse.setCreateBy(accountDO.getCreateBy());
        webAccountDetailResponse.setUpdateBy(accountDO.getUpdateBy());
        webAccountDetailResponse.setCreatedAt(accountDO.getCreatedAt());
        webAccountDetailResponse.setUpdatedAt(accountDO.getUpdatedAt());
        webAccountDetailResponse.setHospitalId(accountDO.getHid());

        webAccountDetailResponse.setOrganizationList(Collections.emptyList());
        webAccountDetailResponse.setDepartmentList(Collections.emptyList());
        webAccountDetailResponse.setRoleList(Collections.emptyList());

//        if (CollectionUtils.isNotEmpty(organizationDOList)) {
//            List<WebAccountDetailResponse.WebAccountOrganizationResponse> organizationList = Lists.newArrayListWithCapacity(organizationDOList.size());
//            organizationDOList.forEach(organizationDO -> organizationList.add(buildWebAccountOrganizationResponse(organizationDO)));
//            webAccountDetailResponse.setOrganizationList(organizationList);
//        }

        if (CollectionUtils.isNotEmpty(departmentDOList)) {
            List<WebAccountDetailResponse.WebAccountOrganizationResponse> organizationList = Lists.newArrayListWithCapacity(departmentDOList.size());
            departmentDOList.forEach(organizationDO -> organizationList.add(buildWebAccountOrganizationResponse(organizationDO)));
            webAccountDetailResponse.setDepartmentList(organizationList);
        }

        if (CollectionUtils.isNotEmpty(roleDOList)) {
            List<WebAccountDetailResponse.WebAccountRoleResponse> roleList = Lists.newArrayListWithCapacity(roleDOList.size());
            roleDOList.forEach(roleDO -> roleList.add(buildWebAccountRoleResponse(roleDO)));
            webAccountDetailResponse.setRoleList(roleList);
        }
        return webAccountDetailResponse;
    }

    private static WebAccountDetailResponse.WebAccountOrganizationResponse buildWebAccountOrganizationResponse(OrganizationDO organizationDO) {
        if (organizationDO == null) {
            return null;
        }
        WebAccountDetailResponse.WebAccountOrganizationResponse webAccountOrganizationResponse = new WebAccountDetailResponse.WebAccountOrganizationResponse();
        webAccountOrganizationResponse.setId(organizationDO.getId());
        webAccountOrganizationResponse.setName(organizationDO.getName());
        webAccountOrganizationResponse.setParentId(organizationDO.getParentId());
        webAccountOrganizationResponse.setLevel(organizationDO.getLevel());
        webAccountOrganizationResponse.setStatus(organizationDO.getStatus());
        return webAccountOrganizationResponse;
    }

    private static WebAccountDetailResponse.WebAccountRoleResponse buildWebAccountRoleResponse(RoleDO roleDO) {
        if (roleDO == null) {
            return null;
        }
        WebAccountDetailResponse.WebAccountRoleResponse webAccountRoleResponse = new WebAccountDetailResponse.WebAccountRoleResponse();
        webAccountRoleResponse.setId(roleDO.getId());
        webAccountRoleResponse.setName(roleDO.getName());
        webAccountRoleResponse.setRoleKey(roleDO.getRoleKey());
        webAccountRoleResponse.setStatus(roleDO.getStatus());
        webAccountRoleResponse.setRemark(roleDO.getRemark());
        return webAccountRoleResponse;
    }

    public static WebAccountBasicResponse buildAccountBasicResponse(AccountDO accountDO) {
        if (accountDO == null) {
            return null;
        }
        WebAccountBasicResponse webAccountBasicResponse = new WebAccountBasicResponse();
        webAccountBasicResponse.setId(accountDO.getId());
        webAccountBasicResponse.setHospitalId(accountDO.getHid());
        webAccountBasicResponse.setNickname(accountDO.getNickname());
        webAccountBasicResponse.setPhoneNumber(accountDO.getPhoneNumber());
        webAccountBasicResponse.setAvatarUrl(accountDO.getAvatarUrl());
        webAccountBasicResponse.setStatus(accountDO.getStatus());
        webAccountBasicResponse.setType(accountDO.getType());
        webAccountBasicResponse.setLastLoginIp(accountDO.getLastLoginIp());
        webAccountBasicResponse.setLastLoginDate(accountDO.getLastLoginDate());
        webAccountBasicResponse.setPasswordUpdateDate(accountDO.getPasswordUpdateDate());
        webAccountBasicResponse.setCreateBy(accountDO.getCreateBy());
        webAccountBasicResponse.setUpdateBy(accountDO.getUpdateBy());
        webAccountBasicResponse.setCreatedAt(accountDO.getCreatedAt());
        webAccountBasicResponse.setUpdatedAt(accountDO.getUpdatedAt());
        return webAccountBasicResponse;
    }
}

# getPlanBeforeTodayTaskWithoutTenant 租户隔离修复

## 🔍 问题描述

用户需要修改 `getPlanBeforeTodayTaskWithoutTenant` 方法，使其不受租户隔离限制，能够查询所有租户的计划任务数据。

## 🔍 问题分析

### 原始实现问题

```java
@Override
public List<PlanTaskVO> getPlanBeforeTodayTaskWithoutTenant(PlanType planType, Long planId) {
    Page<PlanTaskDO> page = new Page<>(1, 1000);
    LambdaQueryWrapper<PlanTaskDO> queryWrapper = planTaskRepositoryService.queryWrapper(planType);
    queryWrapper.eq(PlanTaskDO::getStatus, TaskStatusType.NON_COMPLETED.getCode());
    queryWrapper.eq(PlanTaskDO::getPlanId, planId);
    queryWrapper.orderByDesc(PlanTaskDO::getCreatedAt);
    page = planTaskRepositoryService.page(planType, page, queryWrapper);  // ❌ 仍然受租户隔离影响
    return PageResponse.build(page, PlanTaskBuilder::build).getRecords();
}
```

**问题**：虽然方法名包含 `WithoutTenant`，但实际调用的 `page` 方法仍然会被租户拦截器处理，无法真正忽略租户隔离。

## ✅ 解决方案

### 整体架构

采用分层架构，从底层到顶层逐步添加无租户隔离的支持：

```
Service Layer (PlanTaskServiceImpl)
       ↓
Adaptor Layer (PlanTaskRepositoryAdaptor)
       ↓
Repository Layer (PatrolPlanTaskRepositoryService/MaintenancePlanTaskRepositoryService)
       ↓
Mapper Layer (PatrolPlanTaskMapper/MaintenancePlanTaskMapper)
```

### 1. Mapper层 - 添加忽略租户隔离的SQL方法

#### PatrolPlanTaskMapper.java
```java
/**
 * 忽略租户隔离的分页查询
 * 用于定时任务等需要跨租户查询的场景
 * 
 * @param page 分页参数
 * @param queryWrapper 查询条件
 * @return 分页结果
 */
@InterceptorIgnore(tenantLine = "true")
@Select("SELECT * FROM t_project_patrol_plan_task ${ew.customSqlSegment}")
Page<PatrolPlanTaskDO> selectPageWithoutTenant(@Param("page") Page<PatrolPlanTaskDO> page, @Param("ew") LambdaQueryWrapper<PatrolPlanTaskDO> queryWrapper);
```

#### MaintenancePlanTaskMapper.java
```java
/**
 * 忽略租户隔离的分页查询
 * 用于定时任务等需要跨租户查询的场景
 * 
 * @param page 分页参数
 * @param queryWrapper 查询条件
 * @return 分页结果
 */
@InterceptorIgnore(tenantLine = "true")
@Select("SELECT * FROM t_project_maintenance_plan_task ${ew.customSqlSegment}")
Page<MaintenancePlanTaskDO> selectPageWithoutTenant(@Param("page") Page<MaintenancePlanTaskDO> page, @Param("ew") LambdaQueryWrapper<MaintenancePlanTaskDO> queryWrapper);
```

### 2. Repository层 - 添加接口和实现

#### 接口定义
```java
// PatrolPlanTaskRepositoryService.java
Page<PatrolPlanTaskDO> pageWithoutTenant(Page<PatrolPlanTaskDO> page, LambdaQueryWrapper<PatrolPlanTaskDO> queryWrapper);

// MaintenancePlanTaskRepositoryService.java  
Page<MaintenancePlanTaskDO> pageWithoutTenant(Page<MaintenancePlanTaskDO> page, LambdaQueryWrapper<MaintenancePlanTaskDO> queryWrapper);
```

#### 实现类
```java
// PatrolPlanTaskRepositoryServiceImpl.java
@Override
public Page<PatrolPlanTaskDO> pageWithoutTenant(Page<PatrolPlanTaskDO> page, LambdaQueryWrapper<PatrolPlanTaskDO> queryWrapper) {
    return baseMapper.selectPageWithoutTenant(page, queryWrapper);
}

// MaintenancePlanTaskRepositoryServiceImpl.java
@Override
public Page<MaintenancePlanTaskDO> pageWithoutTenant(Page<MaintenancePlanTaskDO> page, LambdaQueryWrapper<MaintenancePlanTaskDO> queryWrapper) {
    return baseMapper.selectPageWithoutTenant(page, queryWrapper);
}
```

### 3. Adaptor层 - 统一适配不同计划类型

```java
// PlanTaskRepositoryAdaptor.java
/**
 * 忽略租户隔离的分页查询
 * 用于定时任务等需要跨租户查询的场景
 * 
 * @param planType 计划类型
 * @param page 分页参数
 * @param queryWrapper 查询条件
 * @return 分页结果
 */
public Page pageWithoutTenant(PlanType planType, Page page, LambdaQueryWrapper queryWrapper) {
    if (PlanType.PATROL.equals(planType)) {
        return patrolPlanTaskRepositoryService.pageWithoutTenant(page, queryWrapper);
    }
    return maintenancePlanTaskRepositoryService.pageWithoutTenant(page, queryWrapper);
}
```

### 4. Service层 - 修改业务逻辑

```java
// PlanTaskServiceImpl.java
/**
 * 获取今天之前的未完成的所有任务（忽略租户隔离）
 * 
 * 此方法用于定时任务等需要跨租户查询的场景，
 * 能够查询到所有租户下指定计划的未完成任务。
 *
 * @param planType 计划类型（巡检或维保）
 * @param planId 计划ID
 * @return 未完成的任务列表
 */
@Override
public List<PlanTaskVO> getPlanBeforeTodayTaskWithoutTenant(PlanType planType, Long planId) {
    // 1. 创建分页对象（设置较大的页面大小以获取所有数据）
    Page<PlanTaskDO> page = new Page<>(1, 1000);
    
    // 2. 构建查询条件
    LambdaQueryWrapper<PlanTaskDO> queryWrapper = planTaskRepositoryService.queryWrapper(planType);
    queryWrapper.eq(PlanTaskDO::getStatus, TaskStatusType.NON_COMPLETED.getCode());  // 只查询未完成的任务
    queryWrapper.eq(PlanTaskDO::getPlanId, planId);  // 指定计划ID
    queryWrapper.orderByDesc(PlanTaskDO::getCreatedAt);  // 按创建时间倒序
    
    // 3. 执行忽略租户隔离的查询
    page = planTaskRepositoryService.pageWithoutTenant(planType, page, queryWrapper);
    
    // 4. 转换为VO对象并返回
    return PageResponse.build(page, PlanTaskBuilder::build).getRecords();
}
```

## 🔧 核心改进

### 1. 技术实现
- **明确的SQL注解**：使用 `@Select` 注解明确指定SQL，绕过MyBatis-Plus的自动SQL生成
- **@InterceptorIgnore注解**：在Mapper层使用，确保租户拦截器被正确忽略
- **分层架构**：每一层都提供对应的无租户隔离方法，保持架构清晰

### 2. 业务逻辑
- **跨租户查询**：能够查询所有租户的计划任务数据
- **定时任务支持**：专门为定时任务等系统级操作设计
- **条件查询**：仍然支持复杂的查询条件

### 3. 代码质量
- **详细注释**：每个方法都有清晰的注释说明用途和注意事项
- **命名规范**：方法名明确表示忽略租户隔离的特性
- **类型安全**：保持强类型，避免类型转换错误

## 🧪 验证方法

### 1. SQL日志验证
启用SQL日志，检查生成的SQL：

```yaml
logging:
  level:
    com.baomidou.mybatisplus: DEBUG
```

**期望结果**：
```sql
-- 普通查询（有租户隔离）
SELECT * FROM t_project_patrol_plan_task WHERE status = ? AND plan_id = ? AND deleted = ? AND hid = ?

-- 无租户隔离查询
SELECT * FROM t_project_patrol_plan_task WHERE status = ? AND plan_id = ? AND deleted = ?
```

### 2. 功能测试
```java
@Test
public void testGetPlanBeforeTodayTaskWithoutTenant() {
    // 设置当前租户为租户1
    UserContext.setHospitalId(1L);
    
    // 调用无租户隔离方法，应该能查询到所有租户的数据
    List<PlanTaskVO> allTasks = planTaskService.getPlanBeforeTodayTaskWithoutTenant(PlanType.PATROL, planId);
    
    // 调用普通方法，只能查询到当前租户的数据
    List<PlanTaskVO> currentTenantTasks = planTaskService.getPlanBeforeTodayTask(PlanType.PATROL, planId);
    
    // 验证：无租户隔离查询的结果应该 >= 当前租户查询的结果
    assertTrue(allTasks.size() >= currentTenantTasks.size());
}
```

## 🎯 预期效果

修复后，`getPlanBeforeTodayTaskWithoutTenant` 方法应该：

1. ✅ **真正忽略租户隔离** - SQL中不包含 `hid = ?` 条件
2. ✅ **支持跨租户查询** - 能查询所有租户的计划任务数据
3. ✅ **保持查询条件** - 仍然支持状态、计划ID等查询条件
4. ✅ **性能良好** - 使用原生SQL，避免额外的拦截器处理
5. ✅ **架构清晰** - 分层实现，便于维护和扩展

## 📝 注意事项

1. **安全考虑**：确保只在需要跨租户查询的场景下使用此方法
2. **权限控制**：调用此方法的业务逻辑应该有适当的权限检查
3. **数据量考虑**：跨租户查询可能返回大量数据，注意分页处理
4. **定时任务专用**：此方法主要为定时任务等系统级操作设计

现在 `getPlanBeforeTodayTaskWithoutTenant` 方法能够真正忽略租户隔离，获取所有租户的计划任务数据！

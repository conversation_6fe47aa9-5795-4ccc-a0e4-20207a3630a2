package com.avatar.hospital.chaperone.service.nursing.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.avatar.hospital.chaperone.builder.nursing.NursingBuilder;
import com.avatar.hospital.chaperone.builder.nursing.NursingDayBuilder;
import com.avatar.hospital.chaperone.builder.nursing.NursingHospitalBuilder;
import com.avatar.hospital.chaperone.component.baccount.OrganizationComponent;
import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.repository.OrganizationRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDayDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingOrderDO;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingDayRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingHospitalRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingOrderRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingRepositoryService;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.repository.OrderRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;

import com.avatar.hospital.chaperone.request.nursing.NursingAddRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingPagingRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.nursing.*;
import com.avatar.hospital.chaperone.service.nursing.NursingService;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


import java.util.*;
import java.util.stream.Collectors;

/**
 * @author:sp0420
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class NursingServiceImpl implements NursingService {

    private final NursingRepositoryService nursingRepositoryService;
    private final NursingDayRepositoryService nursingDayRepositoryService;
    private final NursingHospitalRepositoryService nursingHospitalRepositoryService;
    private final OrganizationRepositoryService organizationRepositoryService;
    private final OrganizationComponent organizationComponent;
    private final NursingOrderRepositoryService nursingOrderRepositoryService;
    private final OrderRepositoryService orderRepositoryService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public NursingAddResponse add(NursingAddRequest request) {
        //新增护工信息
        Long nursingId = nursingRepositoryService.add(NursingBuilder.buildNursingDO(request));
        //新增护工-医院关联信息
        addNursingHospital(request, nursingId);
        //新增30天排班 - 已移除定时任务功能
        return NursingAddResponse.builder().nursingId(nursingId).build();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public NursingUpdateResponse update(NursingUpdateRequest request) {
        NursingDO nursingDO = nursingRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(nursingDO, ErrorCode.NURSING_NOT_EXIST);
        Boolean result = nursingRepositoryService.incrementUpdate(NursingBuilder.buildNursingDO(request));
        //删除原来关联的医院，并新增新的医院关联
        updateNursingHospital(request);
        //如果名称修改了，更新
        if (!Objects.equals(request.getName(), nursingDO.getName())) {
            NursingDayDO nursingDayDO = new NursingDayDO();
            nursingDayDO.setNursingName(request.getName());
            nursingDayDO.setNursingId(request.getId());
            nursingDayRepositoryService.incrementByNursingUpdate(nursingDayDO);
        }

        return NursingUpdateResponse.builder().success(result).build();
    }

    @Override
    public PageResponse<NursingPagingResponse> paging(NursingPagingRequest request) {
        Long total = nursingRepositoryService.countByB(NursingBuilder.buildNursingDO(request));
        if (Objects.isNull(total) || total <= 0) {
            return new PageResponse<>();
        }

        List<NursingPagingResponse> list = nursingRepositoryService.listByB(NursingBuilder.buildNursingDO(request), request.getPageIndex(), request.getPageSize());
        if (CollectionUtils.isEmpty(list)) {
            return new PageResponse<>();
        }
        list.forEach(i -> {
            if (Objects.nonNull(i.getCertificationPic()) && !StringUtils.isBlank(i.getCertificationPic())) {
                i.setCertificationPicList(JSON.parseArray(i.getCertificationPic(), String.class));
            }
        });
        return PageResponse.build(total, request.getPageIndexLong(), request.getPageSizeLong(), list);
    }

    @Override
    public NursingDetailResponse detail(Long nursingId) {
        NursingDetailResponse nursingDO = nursingRepositoryService.findByBId(nursingId);
        AssertUtils.isNotNull(nursingDO, ErrorCode.NURSING_NOT_EXIST);
        if (Objects.nonNull(nursingDO.getCertificationPic()) && !StringUtils.isBlank(nursingDO.getCertificationPic())) {
            nursingDO.setCertificationPicList(JSON.parseArray(nursingDO.getCertificationPic(), String.class));
        }
        return nursingDO;
    }

    @Override
    public NursingSimpleResponse get(Long nursingId) {
        NursingDO nursingDO = nursingRepositoryService.findById(nursingId);
        NursingSimpleResponse result = null;
        if (Objects.nonNull(nursingDO)) {
            result = NursingBuilder.toNursingSimpleResponse(nursingDO);
        }
        return result;
    }

    @Override
    public Map<Long, NursingSimpleResponse> listMapRef(List<Long> nursingIdList) {
        if (CollectionUtils.isEmpty(nursingIdList)) {
            return Maps.newHashMap();
        }
        Map<Long, NursingSimpleResponse> ref = nursingRepositoryService.findByIdList(nursingIdList)
                .stream()
                .collect(Collectors.toMap(NursingDO::getId, NursingBuilder::toNursingSimpleResponse));
        return ref;
    }

    @Override
    public List<NursingSimpleResponse> list(List<Long> nursingIdList) {
        if (CollectionUtils.isEmpty(nursingIdList)) {
            return Lists.newArrayList();
        }
        List<NursingDO> list = nursingRepositoryService.findByIdList(nursingIdList);
        list = CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
        List<NursingSimpleResponse> result = list.stream()
                .map(NursingBuilder::toNursingSimpleResponse)
                .collect(Collectors.toList());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Long nursingId, Long deletedId) {
        //校验是否还有工单
        //查询t_nursing_order获取所有未删除的order，根据order查询t_order表未完成的订单
        List<NursingOrderDO> nursingOrderList = nursingOrderRepositoryService.findByNursingId(nursingId);
        List<Long> orderIds = nursingOrderList.stream().map(NursingOrderDO::getOrderId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderIds)){
            List<OrderDO> orderList = orderRepositoryService.findByIdList(orderIds);
            if (!CollectionUtils.isEmpty(orderList)) {
                throw BusinessException.of(ErrorCode.NURSING_ORDER_IN_PROGRESS);
            }
        }

        //删除护工信息
        Set<Long> nursingIds = new HashSet<>();
        nursingIds.add(nursingId);
        nursingRepositoryService.deleteByIds(nursingIds, deletedId);

        //删除关联信息
        nursingHospitalRepositoryService.deleteByNursingIds(nursingIds);
        return Boolean.TRUE;
    }

    /**
     * 新增医院关联
     *
     * @param request
     * @return void
     */
    private void addNursingHospital(NursingAddRequest request, Long nursingId) {
        if (CollectionUtils.isEmpty(request.getOrgIds()) || Objects.isNull(nursingId)) {
            return;
        }

        //查询医院是否存在
        Set<Long> organizationIds = new HashSet<>(request.getOrgIds());
        List<OrganizationDO> organizationList = organizationComponent.findHospitalOrganizationList(organizationIds);
        if (CollectionUtils.isEmpty(organizationList)) {
            log.info("NursingServiceImpl add request organization is empty");
            return;
        }

        //关联医院信息
        Set<Long> organizationIdSet = organizationList.stream().map(OrganizationDO::getId).collect(Collectors.toSet());
        nursingHospitalRepositoryService.add(NursingHospitalBuilder.buildNursingHospitalDO(request, organizationIdSet, nursingId));
    }

    /**
     * 更新医院关联
     *
     * @param request
     * @return void
     */
    private void updateNursingHospital(NursingUpdateRequest request) {
        if (CollectionUtils.isEmpty(request.getOrgIds())) {
            return;
        }

        //逻辑删除原来关联的医院
        Set<Long> nursingId = new HashSet<>();
        nursingId.add(request.getId());
        nursingHospitalRepositoryService.deleteByNursingIds(nursingId);

        //查询医院是否存在
        Set<Long> organizationIds = new HashSet<>(request.getOrgIds());
        List<OrganizationDO> organizationList = organizationComponent.findHospitalOrganizationList(organizationIds);
        if (CollectionUtils.isEmpty(organizationList)) {
            log.info("NursingServiceImpl add request organization is empty");
            return;
        }

        //关联医院信息
        Set<Long> organizationIdSet = organizationList.stream().map(OrganizationDO::getId).collect(Collectors.toSet());
        nursingHospitalRepositoryService.add(NursingHospitalBuilder.buildNursingHospitalDO(request, organizationIdSet, request.getId()));
    }


    @Async
    public void addNursingDay(List<String> dateList, Long nursingId, String name, Long createBy) {
        nursingDayRepositoryService.saveBatch(NursingDayBuilder.buildNursingDayDO(dateList, nursingId, name, createBy));
    }
}

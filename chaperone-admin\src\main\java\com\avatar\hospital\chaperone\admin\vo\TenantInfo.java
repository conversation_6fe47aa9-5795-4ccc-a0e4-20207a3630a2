package com.avatar.hospital.chaperone.admin.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 租户信息VO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class TenantInfo {

    /**
     * 租户ID（院区ID）
     */
    private Long tenantId;

    /**
     * 租户名称（院区名称）
     */
    private String tenantName;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 租户状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 租户类型
     */
    private String tenantType;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 地址
     */
    private String address;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用户数量
     */
    private Long userCount;

    /**
     * 计划数量
     */
    private Long planCount;

    /**
     * 设备数量
     */
    private Long deviceCount;

    /**
     * 订单数量
     */
    private Long orderCount;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;

}

package com.avatar.hospital.chaperone.job;

import com.avatar.hospital.chaperone.annotation.Task;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-25 10:59
 **/
@Slf4j
@Component
public class JobTaskRegister implements BeanPostProcessor {

    private final Map<String,JobTaskRegisterMeta> register = new HashMap<>();

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<?> clazz = bean.getClass();
        boolean present = clazz.isAnnotationPresent(Task.class);
        if (present) {
            Method[] methods = clazz.getMethods();
            for (Method method : methods) {
                boolean methodTaskPresent = method.isAnnotationPresent(Task.class);
                if (!methodTaskPresent) {
                    continue;
                }
                Type[] parameterTypes = method.getGenericParameterTypes();
                Task taskAnnotation = method.getAnnotation(Task.class);
                // 生产解析解析数据
                JobTaskRegisterMeta meta = new JobTaskRegisterMeta();
                meta.setCode(taskAnnotation.value());
                meta.setDescription(taskAnnotation.description());
                meta.setBean(bean);
                meta.setMethod(method);
                meta.setParameterTypes(parameterTypes);
                boolean flag = register.containsKey(meta.getCode());
                log.info("JobTaskRegister load description:{}",meta.toStr());
                AssertUtils.isFalse(flag,ErrorCode.PARAMETER_ERROR);
                register.put(meta.getCode(),meta);
            }
        }
        return bean;
    }

    /**
     * 调用
     * @param code
     * @param args
     */
    public Boolean invoke(String code, Object[] args) {
        JobTaskRegisterMeta meta = register.get(code);
        AssertUtils.isNotNull(meta, ErrorCode.PARAMETER_ERROR);
        try {
            int i = 0;
            Object[] realArgs = new Object[args.length];
            for (Type parameterType : meta.getParameterTypes()) {
                realArgs[i] = to(parameterType,args[i]);
                i++;
            }
            meta.getMethod().invoke(meta.getBean(),realArgs);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("JobTaskRegister invoke fail {}", Throwables.getStackTraceAsString(e));
        }
        return Boolean.TRUE;
    }

    /**
     * 参数转换
     * @param parameterType
     * @param val
     * @return
     */
    public Object to(Type parameterType,Object val) {
        if (parameterType.getTypeName().equals(Integer.class.getName())) {
            if ("null".equals(val.toString())) {
                return null;
            }
            return Integer.valueOf(val.toString());
        }
        if (parameterType.getTypeName().equals(Long.class.getName())) {
            if ("null".equals(val.toString())) {
                return null;
            }
            return Long.valueOf(val.toString());
        }
        if (parameterType.getTypeName().equals(String.class.getName())) {
            return val.toString();
        }
        return null;
    }

    /**
     * 查询所有已注册
     * @return
     */
    public Map<String,String> all() {
        Map<String,String> map = Maps.newHashMap();
        register.forEach((code,meta) -> map.put(code,meta.toStr()));
        return map;
    }


}

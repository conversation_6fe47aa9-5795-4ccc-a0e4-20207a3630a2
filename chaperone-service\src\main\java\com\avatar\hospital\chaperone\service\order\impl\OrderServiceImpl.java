package com.avatar.hospital.chaperone.service.order.impl;

import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.builder.order.OrderBuilder;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingOrderRepositoryService;
import com.avatar.hospital.chaperone.database.order.dataobject.*;
import com.avatar.hospital.chaperone.database.order.enums.*;
import com.avatar.hospital.chaperone.database.order.repository.*;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.OrganizationControlRequest;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationControlResponse;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountDetailResponse;
import com.avatar.hospital.chaperone.response.item.ItemResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDetailResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingSimpleResponse;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.baccount.OrganizationControlService;
import com.avatar.hospital.chaperone.service.caccount.ConsumerAccountService;
import com.avatar.hospital.chaperone.service.item.ItemService;
import com.avatar.hospital.chaperone.service.nursing.NursingService;
import com.avatar.hospital.chaperone.service.order.OrderBillService;
import com.avatar.hospital.chaperone.service.order.OrderConsumerlogService;
import com.avatar.hospital.chaperone.service.order.OrderEstimateService;
import com.avatar.hospital.chaperone.service.order.OrderService;
import com.avatar.hospital.chaperone.service.order.dto.OrderChangeLogDTO;
import com.avatar.hospital.chaperone.service.order.dto.OrderPriceCalculationParam;
import com.avatar.hospital.chaperone.service.order.dto.OrderPriceCalculationResult;
import com.avatar.hospital.chaperone.service.order.event.OrderChangelogEvent;

import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import com.avatar.hospital.chaperone.utils.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 11:06
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class OrderServiceImpl implements OrderService {
    private final OrderRepositoryService orderRepositoryService;
    private final NursingOrderRepositoryService nursingOrderRepositoryService;
    private final OrderBillRepositoryService orderBillRepositoryService;
    private final OrderItemRepositoryService orderItemRepositoryService;
    private final OrderNursingRepositoryService orderNursingRepositoryService;
    private final OrderConsumerlogService orderConsumerlogService;
    private final ItemService itemService;
    private final OrderBillService orderBillService;
    private final NursingService nursingService;
    private final OrderPresetValueRepositoryService presetValueRepositoryService;

    private final ConsumerAccountService consumerAccountService;
    private final OrderEstimateService orderEstimateService;
    private final OrganizationControlService organizationControlService;

    @Override
    public OrderIdResponse create(OrderCreateRequest request) {
        if (request.getOperatorUser().isSourceB()) {
            String accountPhone = request.getAccountPhone();
            ConsumerAccountDetailResponse consumerAccountDetail = consumerAccountService.getByMobile(accountPhone);
            AssertUtils.isNotNull(consumerAccountDetail,ErrorCode.ORDER_QUERY_ACCOUNT_BY_MOBILE_NOT_EXIST);
            request.setAccountId(consumerAccountDetail.getId());
        }
        OrganizationControlResponse organizationControl = organizationControlService.getByOrgId(OrganizationControlRequest.build(request.getOrgId()));

        OrderPriceCalculationResponse calculationResponse = calculationTotalPrice(request);
        Long orderId = orderRepositoryService.create(OrderBuilder.createOrderDOByCreate(request,organizationControl),
                OrderBuilder.createTotalOrderBillDOByCreate(request,calculationResponse),
                OrderBuilder.createOrderItemDOByCreate(request,calculationResponse));


        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_CREATE,
                OrderChangeLogDTO.buildByCreate(),
                OrderChangeLogDTO.buildByCreate(orderId,request,calculationResponse,organizationControl),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }


    @Override
    public OrderDetailResponse getById(OrderRequest request) {
        Long orderId = request.getOrderId();

        OrderDO orderDO = getByIdThrow(orderId);
        if (request.getOperatorUser().isSourceC()
                && !Objects.equals(orderDO.getAccountId(),request.getOperator())) {
            log.info("OrderServiceImpl[]getById C端 用户id和操作用户不匹配 >> orderAccountId:{}",orderDO.getAccountId());
            return null;
        }
        // 当前所有护工
        List<OrderNursingDO> curNursingList = orderNursingRepositoryService.getByOrderId(orderId);
        List<NursingSimpleResponse> curNursingResponseList = null;
        if (CollectionUtils.isNotEmpty(curNursingList)) {
            List<Long> nursingIdList = CollUtils.toListLongDistinct(curNursingList, OrderNursingDO::getNursingId);
            curNursingResponseList = nursingService.list(nursingIdList);
            curNursingResponseList.forEach(nursing -> nursing.setTime(0L));
        }

        OrderPresetValueDO preNursing = presetValueRepositoryService.getByOrderNursing(orderId);
        List<NursingSimpleResponse> preNursingResponseList = null;
        if (Objects.nonNull(preNursing)) {
            List<Long> nursingIdList = StrUtils.strToListLong(preNursing.getVal());
            preNursingResponseList = nursingService.list(nursingIdList);
            preNursingResponseList.forEach(nursing -> nursing.setTime(preNursing.getTime()));
        }

        OrderBillDO orderBillDO = orderBillRepositoryService.getTotalByOrderId(orderId);
        OrderBillDO repayBill = orderBillRepositoryService.getRepayByOrderId(orderId);
        List<OrderItemDO> orderItemDOList = orderItemRepositoryService.getByOrderId(orderId);

        List<Long> itemIds = orderItemDOList.stream()
                .map(item -> item.getItemId())
                .collect(Collectors.toList());
        List<ItemResponse> itemResponses = itemService.listByIds(itemIds);

        return OrderBuilder.createOrderDetailResponseByGetById(orderDO,orderBillDO,orderItemDOList,itemResponses,repayBill,
                                curNursingResponseList,preNursingResponseList);
    }

    @Override
    public PageResponse<OrderResponse> pagingForC(OrderCPageRequest request) {
        Page<OrderDO> page = request.ofPage();
        LambdaQueryWrapper<OrderDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderDO::getAccountId,request.getOperator());
        queryWrapper.orderByDesc(OrderDO::getCreatedAt);
        List<Integer> statusList = convetrStatusList(request.getFrontStatus());
        if (!CollUtils.isEmpty(statusList)) {
            queryWrapper.in(OrderDO::getOrderStatus,statusList);
        }
        page = orderRepositoryService.page(page, queryWrapper);
        return PageResponse.build(page,OrderBuilder::createOrderResponseByPagingForC);
    }

    @Override
    public PageResponse<OrderDetailResponse> pagingForCFull(OrderCPageRequest request) {
        Page<OrderDO> page = request.ofPage();
        LambdaQueryWrapper<OrderDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderDO::getAccountId,request.getOperator());
        queryWrapper.orderByDesc(OrderDO::getCreatedAt);
        List<Integer> statusList = convetrStatusList(request.getFrontStatus());
        if (!CollUtils.isEmpty(statusList)) {
            queryWrapper.in(OrderDO::getOrderStatus,statusList);
        }
        page = orderRepositoryService.page(page, queryWrapper);
        List<OrderDetailResponse> list = page.getRecords()
                .stream()
                .map(order -> {
                    OrderRequest orderRequest = OrderRequest.buildById(order.getId());
                    orderRequest.setOperatorUser(request.getOperatorUser());
                    OrderDetailResponse resp = getById(orderRequest);

                    // 订单评价
                    List<OrderEstimateResponse> orderEstimate = orderEstimateService.get(OrderEstimateRequest.buildForC(order.getId()));
                    OrderEstimateResponse estimate = CollUtils.isEmpty(orderEstimate) ? null : orderEstimate.get(0);
                    if (Objects.nonNull(resp)) {
                        resp.setEstimate(estimate);
                    }
                    return resp;
                }).collect(Collectors.toList());
        return PageResponse.build(page,list);
    }

    /**
     * 前端状态转换
     * @param status
     * @return
     */
    public List<Integer> convetrStatusList(Integer status) {
        if (Objects.isNull(status) || Objects.equals(0,status)) {
            return null;
        }
        // 陪护中
        if (Objects.equals(1,status)) {
            return Arrays.asList(OrderStatus.WORK.getStatus(),OrderStatus.APPLY_SETTLE.getStatus());
        }
        // 已结算
        if (Objects.equals(2,status)) {
            return Arrays.asList(OrderStatus.FINISH.getStatus());
        }
        return null;
    }


    /**
     * 计算总价格
     * @param request
     * @return
     */
    private OrderPriceCalculationResponse calculationTotalPrice(OrderCreateRequest request) {
        OrderPriceCalculationRequest calculationRequest = new OrderPriceCalculationRequest();
        calculationRequest.setStartTime(request.getStartTime());
        calculationRequest.setEndTime(request.getEndTime());
        calculationRequest.setItemList(request.getItemIdList());
        OrderPriceCalculationResponse res = priceCalculation(calculationRequest);
        return res;
    }

    @Override
    public OrderPriceCalculationResponse priceCalculation(OrderPriceCalculationRequest request) {
        if (Objects.isNull(request.getStartTime())
                || Objects.isNull(request.getEndTime())
                || CollectionUtils.isEmpty(request.getItemList())) {
            // 无法计算价格场景
            return OrderPriceCalculationResponse.build(0,0,new ArrayList<>());
        }
        List<Long> itemIdList = request.getItemList();
        List<ItemResponse> itemList = itemService.listByIds(itemIdList);
        List<OrderPriceCalculationResponse.PriceCalculationItem> itemPriceList = itemList.stream()
                .map(item -> {
                    Integer intervalDay = DateUtils.intervalDayInt(request.getStartTime(), request.getEndTime(),item.getChargingTime());
                    return OrderPriceCalculationResponse.PriceCalculationItem.build(item,intervalDay,request.getDiscount());
                })
                .collect(Collectors.toList());
        Integer totalPrice = itemPriceList.stream()
                .map(item -> item.getTotalPrice())
                .reduce(0,(o1,o2) -> o1 + o2);

        Integer totalDiscountPrice = itemPriceList.stream()
                .map(item -> item.getTotalDiscountPrice())
                .reduce(0,(o1,o2) -> o1 + o2);

        return OrderPriceCalculationResponse.build(totalPrice,totalDiscountPrice,itemPriceList);
    }

    @Override
    public OrderIdResponse cancel(OrderRequest request) {
        Long orderId = request.getOrderId();
        OrderDO orderDO = getByIdThrow(orderId);
        AssertUtils.isFalse(OrderUtils.isWork((orderDO.getOrderStatus())),ErrorCode.ORDER_ALREADY_CONFIRM_ERROR);

        OrderDO updateEntity = OrderBuilder.createUpdateEntityByCancel(orderDO);
        if (Objects.nonNull(request.getForceCancel())
                && request.getForceCancel()) {
            updateEntity.setOrderStatus(OrderStatus.CANCEL.getStatus());
        }
        updateEntity.setUpdateBy(request.getOperator());
        orderRepositoryService.updateById(updateEntity);

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_CANCEL,
                OrderChangeLogDTO.buildByCancel(orderDO),
                OrderChangeLogDTO.buildByCancel(orderId),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    @Override
    public OrderIdResponse commitHospital(OrderRequest request) {
        Long orderId = request.getOrderId();
        OrderDO orderDO = getByIdThrow(orderId);
        AssertUtils.isTrue(Objects.equals(OrderStatus.of(orderDO.getOrderStatus()),OrderStatus.COMMIT_HOSPITAL),ErrorCode.ORDER_STATUS_ERROR);

        orderItemRepositoryService.checkExist(orderId);

        OrderDO updateEntity = OrderBuilder.createUpdateEntityByStatus(orderId,OrderStatus.COMMIT_CERTIFIED_PROPERTY);
        orderRepositoryService.updateById(updateEntity);

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_COMMIT_HOSPITAL,
                OrderChangeLogDTO.buildByModifyStatus(orderDO),
                OrderChangeLogDTO.buildByModifyStatus(updateEntity),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    @Override
    public OrderIdResponse commitCertifiedProperty(OrderRequest request) {
        Long orderId = request.getOrderId();
        OrderDO orderDO = getByIdThrow(orderId);
        AssertUtils.isTrue(Objects.equals(OrderStatus.of(orderDO.getOrderStatus()),OrderStatus.COMMIT_CERTIFIED_PROPERTY),ErrorCode.ORDER_STATUS_ERROR);

        OrderDO updateEntity = OrderBuilder.createUpdateEntityByStatus(orderId,OrderStatus.COMMIT_COMPLETE_INFO);
        orderRepositoryService.updateById(updateEntity);

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_COMMIT_CERTIFIED_PROPERTY,
                OrderChangeLogDTO.buildByModifyStatus(orderDO),
                OrderChangeLogDTO.buildByModifyStatus(updateEntity),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    @Override
    public OrderIdResponse commitCompleteInfo(OrderRequest request) {
        Long orderId = request.getOrderId();
        Long operator = request.getOperator();
        OrderDO orderDO = getByIdThrow(orderId);
        AssertUtils.isTrue(Objects.equals(OrderStatus.of(orderDO.getOrderStatus()),OrderStatus.COMMIT_COMPLETE_INFO),ErrorCode.ORDER_STATUS_ERROR);

        // 检测信息是否已完成 护工 付费方式 套餐
        orderNursingRepositoryService.checkExist(orderId);
        orderBillRepositoryService.checkExist(orderId);
        orderItemRepositoryService.checkExist(orderId);

        // 计算 - 已移除定时任务功能，需要重新实现价格计算逻辑
        // TODO: 重新实现价格计算逻辑
        OrderDO orderEntity = getByIdThrow(orderId);
        orderEntity.setOrderStatus(OrderStatus.WAIT_CONFIRM.getStatus());

        // 暂时跳过价格计算和账单更新
        orderRepositoryService.updateById(orderEntity);

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_COMMIT_COMPLETE_INFO,
                OrderChangeLogDTO.buildByModifyStatus(orderDO),
                OrderChangeLogDTO.buildByModifyStatus(orderEntity),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public OrderIdResponse confirm(OrderRequest request) {
        Long orderId = request.getOrderId();
        OrderDO orderDO = getByIdThrow(orderId);
        AssertUtils.isTrue(OrderStatus.WAIT_CONFIRM.withEq(orderDO.getOrderStatus()),ErrorCode.ORDER_STATUS_ERROR);
        // 订单已过期
        Boolean expire = LocalDateTime.now().compareTo(DateUtils.parseForIntH(orderDO.getRealEndTime())) >= 0;

        List<OrderNursingDO> orderNursingDOList = orderNursingRepositoryService.getByOrderId(orderId);
        AssertUtils.isTrue(CollectionUtils.isNotEmpty(orderNursingDOList),ErrorCode.ORDER_NOT_BIND_NURSING_ERROR);


        // 修改订单状态
        OrderDO updateEntity = OrderBuilder.createUpdateEntityByConfirm(orderDO,request.getOperator());
        if(expire) { // 已过期
            updateEntity.setOrderStatus(OrderStatus.CANCEL.getStatus());
        }
        orderRepositoryService.updateById(updateEntity);

        if (!expire) { // 未过期
            // 更新预付单开始时间
            orderBillRepositoryService.updateRepayStartTime(orderDO.getId(),updateEntity.getRealStartTime());
            // 生成消耗记录
            OrderConsumeCreateRequest consumeRequest = new OrderConsumeCreateRequest();
            consumeRequest.setOrderId(orderId);
            consumeRequest.setDate(DateUtils.dateInt());
            consumeRequest.setOperatorUser(request.getOperatorUser());
            orderConsumerlogService.createByOrderConform(consumeRequest);
            // 生成/更新 本月排班记录 - 已移除定时任务功能
            // TODO: 如需要护工排班功能，请重新实现
        }

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_CONFIRM,
                OrderChangeLogDTO.buildByConfirm(orderDO),
                OrderChangeLogDTO.buildByConfirm(updateEntity),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    @Override
    public OrderIdResponse applySettle(OrderRequest request) {
        Long orderId = request.getOrderId();
        OrderDO orderDO = getByIdThrow(orderId);
        AssertUtils.isTrue(OrderStatus.WORK.withEq(orderDO.getOrderStatus()),ErrorCode.ORDER_STATUS_ERROR);

        OrderDO updateEntity = OrderBuilder.createUpdateEntityBySettle(orderDO,request.getOperator());
        orderRepositoryService.updateById(updateEntity);

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_APPLY_SETTLE,
                OrderChangeLogDTO.buildBySettle(orderDO),
                OrderChangeLogDTO.buildBySettle(orderId),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    @Override
    public PageResponse<OrderResponse> paging(OrderPageRequest request) {
        Page<OrderDO> page = request.ofPage();
        LambdaQueryWrapper<OrderDO> queryWrapper = queryWrapper();
        queryWrapper.orderByDesc(OrderDO::getCreatedAt);
        queryWrapper.eq(Objects.nonNull(request.getOrderStatus()),OrderDO::getOrderStatus,request.getOrderStatus());
        queryWrapper.eq(Objects.nonNull(request.getId()),OrderDO::getId,request.getId());
        page = orderRepositoryService.page(page, queryWrapper);
        // 添加套餐数据
        List<OrderDO> records = page.getRecords();
        Map<Long, List<OrderItemDO>> orderMap = Maps.newHashMap();
        if (Objects.nonNull(records) && !records.isEmpty()) {
            List<Long> orderIdList = CollUtils.toListLongDistinct(records, OrderDO::getId);
            List<OrderItemDO> orderItemList = orderItemRepositoryService.getByOrderIdList(orderIdList);
            orderMap = orderItemList.stream()
                    .collect(Collectors.groupingBy(OrderItemDO::getOrderId));
        }
        // 封装逻辑
        Map<Long, List<OrderItemDO>> orderMapFinal = orderMap;
        List<OrderResponse> list = new ArrayList<>();
        records.forEach(order -> list.add(OrderBuilder.totoOrderResponseByOrderDOForB(order,orderMapFinal)));

        return PageResponse.build(page,list);
    }

    @Override
    public OrderIdResponse modify(OrderModifyRequest request) {
        Long orderId = request.getId();
        OrderDO orderDO = getByIdThrow(orderId);


        OrderDO updateEntity = OrderBuilder.createOrderDOByModify(request);
        orderRepositoryService.updateById(updateEntity);

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_MODIFY,
                OrderChangeLogDTO.buildByModify(orderDO),
                OrderChangeLogDTO.buildByModify(updateEntity),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    @Override
    public PageResponse<OrderResponse> pagingForConsumerLog(OrderConsumerLogPageRequest request) {
        Page<OrderDO> page = request.ofPage();
        LambdaQueryWrapper<OrderDO> queryWrapper = queryWrapper();
        queryWrapper.in(OrderDO::getOrderStatus, OrderStatus.WORK,OrderStatus.APPLY_SETTLE);
        queryWrapper.orderByDesc(OrderDO::getCreateBy);
        page = orderRepositoryService.page(page, queryWrapper);
        return PageResponse.build(page,OrderBuilder::toOrderResponseByOrderDO);
    }

    @Override
    public OrderIdResponse modifyDiscount(OrderModifyDiscountRequest request) {
        Long orderId = request.getOrderId();
        Long operator = request.getOperator();
        OrderDO orderDO = getByIdThrow(orderId);

        // 订单折扣修改 - 已移除定时任务功能，需要重新实现价格计算逻辑
        // TODO: 重新实现折扣计算逻辑
        OrderDO orderUpdateEntity = getByIdThrow(orderId);
        orderUpdateEntity.setDiscountType(request.getType());
        orderRepositoryService.updateById(orderUpdateEntity);
        // 如果是追溯,判断折扣是否一致,如果不一致,则修改记录,且订单是否在陪护中
        if (OrderDiscountType.of(request.getType()).isAscend()
                && OrderUtils.isWork(orderDO.getOrderStatus())) {
            orderConsumerlogService.resetCalculation(OrderConsumeResetCalculationRequest.build(orderId,request.getOperator()));
        }

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_MODIFY_DISCOUNT,
                OrderChangeLogDTO.buildByModifyDiscount(orderDO),
                OrderChangeLogDTO.buildByModifyDiscount(request),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    @Override
    public OrderIdResponse modifyRate(OrderModifyRateRequest request) {
        Long orderId = request.getOrderId();
        OrderDO orderDO = getByIdThrow(orderId);

        OrderDO updateEntity = OrderBuilder.createByModifyRate(request);
        orderRepositoryService.updateById(updateEntity);
        // 如果是追溯,判断费率是否一致,如果不一致,则修改记录
        if (OrderDiscountType.of(request.getType()).isAscend()
                /*&& (!Objects.equals(request.getRateCertifiedProperty(),orderDO.getRateCertifiedProperty())
                        || !Objects.equals(request.getRateNursing(),orderDO.getRateNursing())
                        || !Objects.equals(request.getRateHospital(),orderDO.getRateHospital()))*/
                && OrderUtils.isWork(orderDO.getOrderStatus())) {
            orderConsumerlogService.resetCalculation(OrderConsumeResetCalculationRequest.build(orderId,request.getOperator()));
        }

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_MODIFY_RATE,
                OrderChangeLogDTO.buildByModifyRate(orderDO),
                OrderChangeLogDTO.buildByModifyRate(request),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    @Override
    public OrderIdResponse modifyNursing(OrderModifyNursingRequest request) {
        Long orderId = request.getOrderId();
        OrderDO order = getByIdThrow(orderId);

        List<Long> nursingIdList = request.getNursingIdList();
        Map<Long,NursingSimpleResponse> nursingRef = nursingService.listMapRef(nursingIdList);
        AssertUtils.isTrue(nursingRef.size() == nursingIdList.size(),ErrorCode.NURSING_NOT_EXIST);

        List<OrderNursingDO> oldNursingDOList = orderNursingRepositoryService.getByOrderId(orderId);
        List<OrderNursingDO> newNursingDOList = OrderBuilder.createOrderNursingDOBymodifyNursing(request,nursingRef);
        if (OrderUtils.isCompleteInfo(order.getOrderStatus())) {
            // 完善信息
            log.info("OrderServiceImpl[]modifyNursing completeInfo");
            orderNursingRepositoryService.add(oldNursingDOList,newNursingDOList);
        } else if (OrderUtils.isWork(order.getOrderStatus())) {
            // 陪护中
            Boolean nowFlag = request.now();
            if (request.getPresetTime() > 0) {
                LocalDateTime localDateTime = DateUtils.toLocalDateTimeForMilliStr(request.getPresetTime());
                LocalDateTime localDateTime2 = DateUtils.toLocalDateTimeHNow();
                nowFlag = localDateTime.compareTo(localDateTime2) <= 0;
            }
            if (nowFlag) {
                log.info("OrderServiceImpl[]modifyNursing now");
                presetValueRepositoryService.clean(request.getOrderId(),OrderPresetValueType.NURSING.getStatus());
                orderNursingRepositoryService.add(oldNursingDOList,newNursingDOList);
                // 更新排班记录 - 已移除定时任务功能
                // TODO: 如需要护工排班功能，请重新实现
            } else {
                // 不是立即 ,走定时任务预先设置
                log.info("OrderServiceImpl[]modifyNursing pre value");
                if (request.clean()) {
                    presetValueRepositoryService.clean(request.getOrderId(),OrderPresetValueType.NURSING.getStatus());
                } else {
                    OrderPresetValueDO presetValue = OrderBuilder.createOrderPresetValue(request);
                    presetValueRepositoryService.add(presetValue);
                }
            }
        }

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_MODIFY_NURSING,
                OrderChangeLogDTO.buildByModifyNursingBefore(orderId,oldNursingDOList),
                OrderChangeLogDTO.buildByModifyNursingAfter(orderId,newNursingDOList,request.getPresetTime()),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    @Override
    public OrderIdResponse modifyItem(OrderModifyItemRequest request) {
        Long orderId = request.getOrderId();
        Long operator = request.getOperator();
        OrderDO orderDO = getByIdThrow(orderId);
        List<OrderItemDO> list = orderItemRepositoryService.getByOrderId(orderId);
        OrderBillDO totalBill = orderBillRepositoryService.getTotalByOrderId(orderId);
        List<Long> itemIdList = CollUtils.toListLongDistinct(list,OrderItemDO::getItemId);
        itemIdList.addAll(request.getItemIdList());
        Map<Long, ItemResponse> itemMap = itemService.listByIds(itemIdList).stream()
                .collect(Collectors.toMap(ItemResponse::getId, Function.identity()));

        // 订单项目修改价格计算 - 已移除定时任务功能，需要重新实现价格计算逻辑
        // TODO: 重新实现项目修改后的价格计算逻辑
        OrderDO newOrder = getByIdThrow(orderId);
        OrderBillDO newTotalBill = totalBill; // 暂时使用原账单
        List<OrderItemDO> newList = list; // 暂时使用原项目列表
        orderRepositoryService.updateById(newOrder);

        SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_MODIFY_ITEM,
                OrderChangeLogDTO.buildByModifyItem(orderDO,totalBill,list,itemMap),
                OrderChangeLogDTO.buildByModifyItem(newOrder,newTotalBill,newList,itemMap),
                request.getOperatorUser()));
        return OrderIdResponse.build(orderId);
    }

    /**
     * 生成结算单
     * 修改订单状态
     * 更新排班记录
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public OrderIdResponse commitSettle(OrderSettleApplyRequest request) {
        log.info("OrderServiceImpl[]commitSettle start>> request:{}",JSON.toJSONString(request));
        Long orderId = request.getOrderId();
        OrderDO orderDO = getByIdThrow(orderId);
        AssertUtils.isTrue(OrderUtils.isWork(orderDO.getOrderStatus()),ErrorCode.ORDER_NOT_APPLY_SETTLE_ERROR);

        if (request.isPass()) {
            log.info("OrderServiceImpl[]commitSettle 通过");
            OrderConsumeSumPriceResponse consumeSumPriceResponse = orderConsumerlogService.sumPrice(OrderConsumeSumPriceRequest.build(orderId));
            Integer consumerPrice = consumeSumPriceResponse.getPrice();

            OrderBillDO totalBill = orderBillRepositoryService.getTotalByOrderId(orderId);
            List<OrderBillDO> subBillList = orderBillRepositoryService.findAllSubBillByOrderId(orderId);
            int billPriceSum = subBillList.stream()
                    .mapToInt(bill -> bill.getPriceReceivable())
                    .sum();
            // 落库 生成结算账单 把排班关联数据解除 更新陪护单状态
            orderDO.setOrderStatus(OrderStatus.FINISH.getStatus());
            orderDO.setRealEndTime(DateUtils.dateHourInt());

            OrderBillCreateRequest settleBillReq
                    = OrderBuilder.createOrderBillCreateRequestBySettle(orderDO,totalBill,consumerPrice,billPriceSum,request.getOperatorUser());
            orderBillService.create(settleBillReq);
            nursingOrderRepositoryService.clean(orderId,DateUtils.dateIntPlusDay1());

            orderDO.setOrderStatus(OrderStatus.FINISH.getStatus());
            orderDO.setRealEndTime(DateUtils.dateHourInt());
            orderRepositoryService.updateById(orderDO);

            SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_GENERATOR_SETTLE,
                    OrderChangeLogDTO.buildByModifySettleBefore(orderId,orderDO.getOrderStatus()),
                    OrderChangeLogDTO.buildByModifySettleAfter(orderId),
                    request.getOperatorUser()));
        }

        if (request.isReject()) {
            log.info("OrderServiceImpl[]commitSettle 驳回");
            orderDO.setOrderStatus(OrderStatus.WORK.getStatus());
            orderRepositoryService.updateById(orderDO);

            SpringUtils.publishEvent(OrderChangelogEvent.build(this,OrderLogEvent.ORDER_SETTLE_REJECT,
                    OrderChangeLogDTO.buildByRejectSettleApplyBefore(orderId,orderDO.getOrderStatus()),
                    OrderChangeLogDTO.buildByRejectSettleApplyAfter(orderId),
                    request.getOperatorUser()));
        }
        return OrderIdResponse.build(orderId);
    }

    /**
     * 获取陪护单ID
     * @param orderId
     * @return
     */
    private OrderDO getByIdThrow(Long orderId) {
        OrderDO orderDO = orderRepositoryService.getById(orderId);
        AssertUtils.isNotNull(orderDO,ErrorCode.ORDER_NOT_EXIST);
        return orderDO;
    }

    private LambdaQueryWrapper<OrderDO> queryWrapper() {
        LambdaQueryWrapper<OrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderDO::getDeleted,DelUtils.NO_DELETED);
        return queryWrapper;
    }
}

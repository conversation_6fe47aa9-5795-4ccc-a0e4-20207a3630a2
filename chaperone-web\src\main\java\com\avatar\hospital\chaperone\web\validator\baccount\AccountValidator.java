package com.avatar.hospital.chaperone.web.validator.baccount;

import com.avatar.hospital.chaperone.context.UserContext;
import com.avatar.hospital.chaperone.database.baccount.enums.AccountStatus;
import com.avatar.hospital.chaperone.database.baccount.enums.AccountType;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.WebAccountAddRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountAllocationRoleRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountPagingRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountUpdatePasswordRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountUpdateRequest;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.PasswordUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import org.apache.commons.collections4.CollectionUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/10
 */
public class AccountValidator {

    private static final Integer DEFAULT_STATUS = AccountStatus.ENABLE.getStatus();

    public static void addValidate(WebAccountAddRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);

//        if (CollectionUtils.isEmpty(request.getOrganizationIds()) && CollectionUtils.isEmpty(request.getDepartmentIds())) {
//            throw BusinessException.of(ErrorCode.PARAMETER_ERROR);
//        }


        AssertUtils.hasText(request.getNickname(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getPhoneNumber(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getPassword(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isTrue(PasswordUtils.passwordLengthValidate(request.getPassword()), ErrorCode.WEB_ACCOUNT_PASSWORD_LENGTH_ERROR);

        AssertUtils.isNotNull(request.getType(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(AccountType.of(request.getType()), ErrorCode.PARAMETER_ERROR);
        if (request.getStatus() != null) {
            AssertUtils.isNotNull(AccountStatus.of(request.getStatus()), ErrorCode.PARAMETER_ERROR);
        } else {
            request.setStatus(DEFAULT_STATUS);
        }
        // 院区
        request.setHospitalId(UserContext.getHospitalId());
        // 设置创建人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setCreateBy(accountId);
        request.setUpdateBy(accountId);

        // 密码加密
        request.setPassword(PasswordUtils.encrypt(request.getPassword()));
    }

    public static void updateValidate(WebAccountUpdateRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getId(), ErrorCode.PARAMETER_ERROR);

        AssertUtils.hasText(request.getNickname(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getPhoneNumber(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(AccountType.of(request.getType()), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(AccountStatus.of(request.getStatus()), ErrorCode.PARAMETER_ERROR);
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }


    public static void deleteValidate(WebAccountDeleteRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.notEmpty(request.getIds(), ErrorCode.PARAMETER_ERROR);
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    public static void pagingValidate(WebAccountPagingRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageIndex(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageSize(), ErrorCode.PARAMETER_ERROR);
    }

    public static void allocationRoleValidate(WebAccountAllocationRoleRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getAccountId(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.notEmpty(request.getRoleIds(), ErrorCode.PARAMETER_ERROR);
    }

    public static void changePasswordValidate(WebAccountUpdatePasswordRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getAccountId(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getNewPassword(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isTrue(PasswordUtils.passwordLengthValidate(request.getNewPassword()), ErrorCode.WEB_ACCOUNT_PASSWORD_LENGTH_ERROR);
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }
}

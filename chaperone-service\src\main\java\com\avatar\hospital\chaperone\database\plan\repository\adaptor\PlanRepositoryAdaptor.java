package com.avatar.hospital.chaperone.database.plan.repository.adaptor;

import com.avatar.hospital.chaperone.context.UserContext;
import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.database.plan.repository.MaintenancePlanRepositoryService;
import com.avatar.hospital.chaperone.database.plan.repository.PatrolPlanRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 巡检计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Component
public class PlanRepositoryAdaptor {

    @Autowired
    public PatrolPlanRepositoryService patrolPlanRepositoryService;
    @Autowired
    public MaintenancePlanRepositoryService maintenancePlanRepositoryService;

    public boolean save(PlanType planType, PlanDO planDO) {
        // 绑定院区
        planDO.setHid(UserContext.getHospitalId());
        // 巡检计划
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRepositoryService.save((PatrolPlanDO) planDO);
        }
        // 维保计划
        return maintenancePlanRepositoryService.save((MaintenancePlanDO) planDO);
    }

    public PlanDO getById(PlanType planType, Long id) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRepositoryService.getById(id);
        }
        return maintenancePlanRepositoryService.getById(id);
    }

    public boolean update(PlanRequest request) {
        if (PlanType.PATROL.equals(request.getPlanType())) {
            return patrolPlanRepositoryService.update(request);

        }
        return maintenancePlanRepositoryService.update(request);
    }

    public Page page(PlanType planType, Page page, LambdaQueryWrapper queryWrapper) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRepositoryService.page(page, queryWrapper);

        }
        return maintenancePlanRepositoryService.page(page, queryWrapper);
    }

    public List<PlanDO> list(PlanType planType, LambdaQueryWrapper queryWrapper) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRepositoryService.list(queryWrapper);
        }
        return maintenancePlanRepositoryService.list(queryWrapper);
    }

    // 获取所有列表（去除租户隔离）
    public List<PlanDO> getListWithoutTenant(PlanType planType, LambdaQueryWrapper queryWrapper) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRepositoryService.selectAllWithoutTenant(queryWrapper);
        }
        return maintenancePlanRepositoryService.selectAllWithoutTenant(queryWrapper);
    }

    public LambdaQueryWrapper queryWrapper(PlanType planType) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRepositoryService.queryWrapper();
        }
        return maintenancePlanRepositoryService.queryWrapper();
    }

    public boolean abandon(PlanRequest request) {
        if (PlanType.PATROL.equals(request.getPlanType())) {
            return patrolPlanRepositoryService.abandon(request);

        }
        return maintenancePlanRepositoryService.abandon(request);
    }
}



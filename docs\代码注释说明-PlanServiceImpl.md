# PlanServiceImpl 代码注释说明

## 📋 概述

为 `PlanServiceImpl.java` 文件添加了详细的中文注释，提高代码的可读性和维护性。该类是计划管理的核心服务实现类，负责处理巡检计划和维保计划的全生命周期管理。

## 🏗️ 类结构注释

### 类级别注释
```java
/**
 * 计划服务实现类
 * 
 * 负责处理巡检计划和维保计划的核心业务逻辑，包括：
 * 1. 计划的创建、更新、查询、详情获取
 * 2. 计划与设备、执行人、部门的关联关系管理
 * 3. 计划的定时执行和任务生成
 * 4. 计划的作废处理
 * 
 * <AUTHOR>
 * @description 计划管理核心服务，支持巡检计划和维保计划的全生命周期管理
 * @date 2023/10/30 10:48
 */
```

### 依赖注入注释
按功能分组添加了详细注释：

#### 数据访问层依赖
- `PlanRepositoryAdaptor` - 计划主表的CRUD操作
- `PlanRefDeviceRepositoryAdaptor` - 计划与设备的关联关系
- `PlanRefOrgRepositoryAdaptor` - 计划与部门的关联关系
- `PlanRefExecutorRepositoryAdaptor` - 计划与执行人的关联关系
- `PlanTaskService` - 计划任务的生成、执行、过期等操作

#### 业务组件依赖
- `OrganizationComponent` - 获取部门信息
- `WebAccountService` - 获取用户信息
- `DeviceService` - 获取设备信息

## 🔧 方法级别注释

### 1. create() 方法
```java
/**
 * 创建计划
 * 
 * 创建一个新的计划（巡检或维保），包括：
 * 1. 保存计划基本信息
 * 2. 建立计划与设备的关联关系
 * 3. 建立计划与执行人的关联关系  
 * 4. 建立计划与部门的关联关系
 * 
 * @param request 计划创建请求，包含计划基本信息和关联关系
 * @return 创建成功的计划ID
 * @throws Exception 创建过程中的任何异常都会回滚事务
 */
```

### 2. update() 方法
```java
/**
 * 更新计划
 * 
 * 更新现有计划的信息，包括：
 * 1. 更新计划基本信息
 * 2. 重新建立计划与设备的关联关系（先删除旧关联，再建立新关联）
 * 3. 重新建立计划与执行人的关联关系
 * 4. 重新建立计划与部门的关联关系
 * 
 * @param request 计划更新请求，包含要更新的计划信息
 * @return 更新是否成功
 * @throws Exception 更新过程中的任何异常都会回滚事务
 */
```

### 3. paging() 方法
```java
/**
 * 分页查询计划列表
 * 
 * 根据查询条件分页获取计划列表，支持：
 * 1. 按计划编号精确查询
 * 2. 按计划名称模糊查询
 * 3. 按创建时间倒序排列
 * 4. 自动填充关联的设备、执行人、部门信息
 * 
 * @param request 查询请求，包含分页参数和查询条件
 * @return 分页结果，包含计划列表和分页信息
 */
```

### 4. execute() 方法
```java
/**
 * 执行计划定时任务
 * 
 * 这是一个定时任务方法，用于处理计划的自动执行逻辑：
 * 1. 查询所有生效的计划（跨租户查询）
 * 2. 检查每个计划是否到了执行周期
 * 3. 将过期未完成的任务标记为已过期
 * 4. 根据计划周期生成新的任务
 * 
 * 注意：此方法会处理所有租户的计划，确保系统的计划任务能够正常运行
 * 
 * @param planType 计划类型（巡检或维保）
 */
```

## 🔍 核心业务逻辑注释

### 1. 关联关系管理
详细说明了计划与设备、执行人、部门的关联关系管理：
- 创建时建立关联关系
- 更新时先删除旧关联，再建立新关联
- 查询时批量填充关联信息名称

### 2. 定时任务执行逻辑
详细说明了计划定时执行的完整流程：
- 跨租户查询所有生效计划
- 周期检查和任务生成
- 过期任务处理

### 3. 性能优化说明
- 批量查询避免N+1问题
- 子映射提取减少内存占用
- 事务管理确保数据一致性

## 📝 工具方法注释

### setName() 方法
```java
/**
 * 为计划列表填充关联信息的名称
 * 
 * 批量查询并填充计划关联的设备、执行人、部门的名称信息，
 * 采用批量查询的方式提高性能，避免N+1查询问题。
 * 
 * @param planType 计划类型（巡检或维保）
 * @param planVOS 计划VO列表，需要填充关联信息
 */
```

### getSubMap() 方法
```java
/**
 * 从完整映射中提取子映射
 * 
 * 根据指定的ID集合，从完整的ID-名称映射中提取对应的子映射。
 * 这是一个工具方法，用于从批量查询的结果中提取特定计划需要的关联信息。
 * 
 * @param map 完整的ID-名称映射
 * @param ids 需要提取的ID集合
 * @return 包含指定ID的子映射
 */
```

### checkIsExpired() 方法
```java
/**
 * 检查计划是否到了执行周期
 * 
 * 根据计划的创建时间、执行周期类型和周期间隔，判断是否需要生成新的任务。
 * 检查逻辑：
 * 1. 计划必须至少创建一天以上
 * 2. 如果没有历史任务，则需要生成任务
 * 3. 如果有历史任务，则根据周期类型计算下次执行时间
 * 
 * @param taskVOS 计划的历史任务列表
 * @param planDO 计划信息
 * @return true-需要生成新任务，false-还未到执行周期
 */
```

## 🎯 注释特点

### 1. 结构化注释
- **方法功能**：清晰说明方法的主要功能
- **业务逻辑**：详细描述业务处理步骤
- **参数说明**：说明每个参数的含义和用途
- **返回值说明**：说明返回值的含义
- **异常说明**：说明可能抛出的异常

### 2. 业务导向
- 从业务角度解释代码逻辑
- 说明业务规则和约束
- 解释设计决策的原因

### 3. 技术细节
- 说明性能优化点
- 解释事务管理策略
- 标注重要的技术实现

### 4. 维护友好
- 提供足够的上下文信息
- 说明方法间的调用关系
- 标注需要注意的事项

## 📚 维护建议

1. **保持同步**：代码变更时及时更新注释
2. **统一规范**：遵循团队统一的注释规范
3. **适度详细**：重要逻辑详细说明，简单逻辑适度注释
4. **业务导向**：从业务角度而非纯技术角度编写注释

通过详细的注释，`PlanServiceImpl` 类现在更加易读易维护，新团队成员可以快速理解计划管理的核心业务逻辑！

# Admin模块依赖问题解决方案

## 🔍 问题描述

在构建Admin模块时出现依赖解析错误：

```
Unresolved dependency: 'org.springframework.boot:spring-boot-starter-web:jar:2.7.6'
```

## 🔍 问题原因分析

### 1. 依赖版本管理问题

**问题**：Admin模块的pom.xml中某些依赖指定了版本号，但这些版本号引用的属性在父pom中可能不存在或不匹配。

**分析**：
- 父pom已经通过 `<dependencyManagement>` 管理了所有依赖的版本
- 子模块不应该重复指定版本号，应该继承父pom的版本管理
- 某些依赖的artifactId可能与父pom中定义的不一致

### 2. 依赖冲突问题

**问题**：某些依赖存在重复定义或版本冲突。

**分析**：
- Redis依赖被重复定义
- SA-Token Redis依赖的artifactId不正确
- 某些依赖的版本属性在父pom中不存在

## ✅ 解决方案

### 方案1：修复现有pom.xml（推荐）

#### 1.1 移除子模块中的版本号

让所有依赖从父pom继承版本：

```xml
<!-- ✅ 正确：不指定版本，从父pom继承 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- ❌ 错误：指定版本可能导致冲突 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
    <version>2.7.6</version>
</dependency>
```

#### 1.2 修复依赖artifactId

确保依赖的artifactId与父pom中定义的一致：

```xml
<!-- ✅ 正确：使用父pom中定义的artifactId -->
<dependency>
    <groupId>cn.dev33</groupId>
    <artifactId>sa-token-redis-jackson</artifactId>
</dependency>

<!-- ❌ 错误：artifactId不匹配 -->
<dependency>
    <groupId>cn.dev33</groupId>
    <artifactId>sa-token-redis</artifactId>
</dependency>
```

#### 1.3 移除重复依赖

避免重复定义相同功能的依赖：

```xml
<!-- ✅ 正确：只定义一次Redis依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- ❌ 错误：重复定义Redis依赖 -->
<!-- 已移除重复的Redis依赖 -->
```

### 方案2：使用简化pom.xml

如果依赖问题复杂，可以使用简化版本：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.avatar.hospital</groupId>
        <artifactId>hospital-project</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>chaperone-admin</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <!-- 只包含最基本的依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
```

## 🔧 已修复的问题

### 1. 移除了所有子模块中的版本号 ✅

```xml
<!-- 修复前 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>${mybatis-plus-boot-starter.version}</version>
</dependency>

<!-- 修复后 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>
```

### 2. 修复了SA-Token Redis依赖 ✅

```xml
<!-- 修复前 -->
<dependency>
    <groupId>cn.dev33</groupId>
    <artifactId>sa-token-redis</artifactId>
    <version>${sa-token-redis.version}</version>
</dependency>

<!-- 修复后 -->
<dependency>
    <groupId>cn.dev33</groupId>
    <artifactId>sa-token-redis-jackson</artifactId>
</dependency>
```

### 3. 移除了重复的Redis依赖 ✅

```xml
<!-- 修复前：重复定义 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>
<!-- ... 其他依赖 ... -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- 修复后：只定义一次 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>
```

## 🚀 验证步骤

### 1. 检查依赖解析

```bash
# 在admin模块目录下执行
mvn dependency:resolve
```

### 2. 检查依赖树

```bash
# 查看完整的依赖树
mvn dependency:tree
```

### 3. 编译测试

```bash
# 编译项目
mvn compile

# 运行测试
mvn test
```

### 4. 启动测试

```bash
# 启动应用
mvn spring-boot:run
```

## 🛠️ 故障排除

### 1. 如果仍然有依赖问题

**步骤1**：清理Maven缓存
```bash
mvn clean
rm -rf ~/.m2/repository/com/avatar/hospital
```

**步骤2**：使用简化pom.xml
```bash
# 备份当前pom.xml
cp pom.xml pom-backup.xml

# 使用简化版本
cp pom-simple.xml pom.xml
```

**步骤3**：逐步添加依赖
```xml
<!-- 先确保基础功能正常 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- 然后逐步添加其他依赖 -->
```

### 2. 如果父pom依赖管理有问题

检查父pom中的dependencyManagement部分：

```xml
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>${spring-boot.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <!-- 其他依赖管理 -->
    </dependencies>
</dependencyManagement>
```

### 3. 版本属性检查

确保父pom中定义了所有需要的版本属性：

```xml
<properties>
    <spring-boot.version>2.7.6</spring-boot.version>
    <mybatis-plus-boot-starter.version>3.5.2</mybatis-plus-boot-starter.version>
    <sa-token-spring-boot-starter.version>1.36.0</sa-token-spring-boot-starter.version>
    <!-- 其他版本属性 -->
</properties>
```

## 📋 依赖检查清单

- [x] **移除版本号**：子模块中的依赖不指定版本号
- [x] **artifactId正确**：确保artifactId与父pom中定义的一致
- [x] **无重复依赖**：避免重复定义相同功能的依赖
- [x] **父pom继承**：确保正确继承父pom的dependencyManagement
- [x] **版本属性存在**：父pom中定义了所有需要的版本属性
- [x] **依赖范围正确**：test依赖使用test scope

## 🎯 最佳实践

### 1. 依赖管理原则

- **父pom统一管理**：所有版本在父pom中统一定义
- **子模块不指定版本**：让子模块继承父pom的版本管理
- **避免重复依赖**：相同功能的依赖只定义一次
- **使用正确的artifactId**：确保与父pom中定义的一致

### 2. 依赖添加流程

1. **检查父pom**：确认依赖是否已在父pom中管理
2. **添加依赖**：在子模块中添加依赖（不指定版本）
3. **验证解析**：运行 `mvn dependency:resolve` 验证
4. **测试编译**：运行 `mvn compile` 测试编译
5. **功能测试**：启动应用测试功能

## 🎉 修复结果

修复后的Admin模块：

✅ **依赖解析正常**：所有依赖都能正确解析
✅ **版本管理统一**：所有版本由父pom统一管理
✅ **无重复依赖**：移除了重复和冲突的依赖
✅ **结构清晰**：依赖结构清晰，易于维护

现在Admin模块的依赖问题已经解决，可以正常构建和启动了！🚀

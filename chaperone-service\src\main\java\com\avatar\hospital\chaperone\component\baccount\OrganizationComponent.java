package com.avatar.hospital.chaperone.component.baccount;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountOrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.enums.OrganizationLevel;
import com.avatar.hospital.chaperone.database.baccount.enums.OrganizationStatus;
import com.avatar.hospital.chaperone.database.baccount.enums.OrganizationType;
import com.avatar.hospital.chaperone.database.baccount.repository.AccountOrganizationRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.OrganizationRepositoryService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/26
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OrganizationComponent {

    private final OrganizationRepositoryService organizationRepositoryService;
    private final AccountOrganizationRepositoryService accountOrganizationRepositoryService;

    /**
     * 查询全部医院层级列表
     *
     * @return -
     */
    public List<OrganizationDO> findAllHospitalOrganizationList() {
        return organizationRepositoryService.findByLevelAndTypesAndStatus(OrganizationLevel.HOSPITAL.getLevel()
                , Sets.newHashSet(OrganizationType.HOSPITAL.getType())
                , OrganizationStatus.ENABLE.getStatus()
        );
    }

    /**
     * 查询全部部门层级列表
     *
     * @return -
     */
    public List<OrganizationDO> findAllDepartmentOrganizationList() {
        return organizationRepositoryService.findByLevelAndTypesAndStatus(OrganizationLevel.HOSPITAL.getLevel()
                , Sets.newHashSet(OrganizationType.DEPARTMENT.getType())
                , OrganizationStatus.ENABLE.getStatus()
        );
    }

    /**
     * 根据组织机构ID查询医院层级列表
     *
     * @param organizations -
     * @return -
     */
    public List<OrganizationDO> findHospitalOrganizationList(Set<Long> organizations) {
        return organizationRepositoryService.findByIdsAndLevelAndTypesAndStatus(organizations
                , OrganizationLevel.HOSPITAL.getLevel()
                , Sets.newHashSet(OrganizationType.HOSPITAL.getType())
                , OrganizationStatus.ENABLE.getStatus()
        );
    }

    /**
     * 根据组织机构ID查询医院&部门层级列表
     *
     * @param organizations -
     * @return -
     */
    public List<OrganizationDO> findHospitalAndDepartmentOrganizationList(Set<Long> organizations) {
        return organizationRepositoryService.findByIdsAndLevel(organizations, OrganizationLevel.HOSPITAL.getLevel());
    }

    /**
     * 查询所有的
     *
     * @param organizations -
     * @return -
     */
    public List<OrganizationDO> findDepartmentOrganizationList(Set<Long> organizations) {
        return organizationRepositoryService.findByIds(organizations);
    }

    /**
     * 查询机构映射
     *
     * @param organizations
     * @return
     */
    public Map<Long, String> findOrgMap(Set<Long> organizations) {
        if (CollectionUtils.isEmpty(organizations)) {
            return Maps.newHashMap();
        }
        Map<Long, String> map = this.findDepartmentOrganizationList(organizations).stream()
                .collect(Collectors.toMap(OrganizationDO::getId, OrganizationDO::getName));
        return map;
    }

    /**
     * 根据用户ID查询部门ID
     *
     * @param accountId -
     * @return -
     */
    public Set<Long> findDepartmentOrganizationList(Long accountId) {
        if (accountId == null) {
            return Collections.emptySet();
        }
        List<AccountOrganizationDO> accountOrganizationDOList = accountOrganizationRepositoryService.findByAccountId(accountId);
        Set<Long> organizationIds = accountOrganizationDOList.stream().map(AccountOrganizationDO::getOrganizationId).collect(Collectors.toSet());

        List<OrganizationDO> organizationDOList = organizationRepositoryService.findByIdsAndLevelAndTypes(organizationIds
                , OrganizationLevel.HOSPITAL.getLevel()
                , Sets.newHashSet(OrganizationType.DEPARTMENT.getType()));
        if (CollectionUtils.isEmpty(organizationDOList)) {
            return Collections.emptySet();
        }
        return organizationDOList.stream().map(OrganizationDO::getId).collect(Collectors.toSet());
    }

    /**
     * 根据用户ID查询所有的组织机构ID
     *
     * @param accountId -
     * @return -
     */
    public Set<Long> findAccountOrganizationIds(Long accountId) {
        if (accountId == null) {
            return Collections.emptySet();
        }
        List<AccountOrganizationDO> accountOrganizationList = accountOrganizationRepositoryService.findByAccountId(accountId);
        if (CollectionUtils.isEmpty(accountOrganizationList)) {
            log.warn("OrganizationComponent findAccountOrganizationIds accountOrganizationList empty accountId:{}", accountId);
            return Collections.emptySet();
        }
        return accountOrganizationList.stream().map(AccountOrganizationDO::getOrganizationId).collect(Collectors.toSet());
    }

    /**
     * B端查询医院层级下面的科室列表
     *
     * @param hospitalOrganizationId -
     * @return -
     */
    public List<OrganizationDO> findWebAdministrativeOfficeOrganizationList(Long hospitalOrganizationId) {
        return organizationRepositoryService.findByParentIdAndLevelAndStatus(hospitalOrganizationId
                , OrganizationLevel.ADMINISTRATIVE_OFFICE.getLevel()
                , OrganizationStatus.ENABLE.getStatus());
    }

    /**
     * C端查询医院层级下面的科室列表
     *
     * @param hospitalOrganizationId -
     * @return -
     */
    public List<OrganizationDO> findConsumerAdministrativeOfficeOrganizationList(Long hospitalOrganizationId) {
        return organizationRepositoryService.findByParentIdAndLevelAndStatus(hospitalOrganizationId
                , OrganizationLevel.ADMINISTRATIVE_OFFICE.getLevel()
                , OrganizationStatus.ENABLE.getStatus());
    }
}

package com.avatar.hospital.chaperone.response.hospital;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class HospitalAddResponse implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

}

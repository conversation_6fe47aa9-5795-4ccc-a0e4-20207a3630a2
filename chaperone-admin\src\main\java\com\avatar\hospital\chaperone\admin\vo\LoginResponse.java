package com.avatar.hospital.chaperone.admin.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 登录响应VO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class LoginResponse {

    /**
     * 访问令牌
     */
    private String token;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    @Data
    public static class UserInfo {
        /**
         * 用户ID
         */
        private Long id;

        /**
         * 用户名
         */
        private String username;

        /**
         * 真实姓名
         */
        private String realName;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 头像
         */
        private String avatar;

        /**
         * 角色
         */
        private String role;

        /**
         * 权限范围
         */
        private String permissionScope;

        /**
         * 最后登录时间
         */
        private LocalDateTime lastLoginTime;
    }

}

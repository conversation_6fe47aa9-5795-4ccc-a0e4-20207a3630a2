package com.avatar.hospital.chaperone.service.part.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.avatar.hospital.chaperone.builder.part.PartStockApplyBuilder;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.enums.PartStockApplyStatusType;
import com.avatar.hospital.chaperone.database.part.repository.PartBatchRepositoryService;
import com.avatar.hospital.chaperone.database.part.repository.PartStockApplyRefPartBatchRepositoryService;
import com.avatar.hospital.chaperone.database.part.repository.PartStockApplyRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.part.PartStockApplyAuditRequest;
import com.avatar.hospital.chaperone.request.part.PartStockApplyRequest;
import com.avatar.hospital.chaperone.request.part.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.part.PartStockApplyVO;
import com.avatar.hospital.chaperone.service.baccount.WebAccountService;
import com.avatar.hospital.chaperone.service.part.PartBatchService;
import com.avatar.hospital.chaperone.service.part.PartStockApplyService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.avatar.hospital.chaperone.enums.ErrorCode.PROJECT_PART_STOCK_APPLY_NOT_EXIST;
import static com.avatar.hospital.chaperone.enums.ErrorCode.PROJECT_PART_STOCK_APPLY_STATUS_NOT_VALID;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 18:06
 */
@Service
public class PartStockApplyServiceImpl implements PartStockApplyService {

    @Autowired
    PartBatchRepositoryService partBatchRepositoryService;
    @Autowired
    PartBatchService partBatchService;
    @Autowired
    PartStockApplyRepositoryService partStockApplyRepositoryService;
    @Autowired
    PartStockApplyRefPartBatchRepositoryService refPartBatchRepositoryService;
    @Autowired
    WebAccountService webAccountService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long create(PartStockApplyRequest request) {
        PartStockApplyDO stockApplyDO = PartStockApplyBuilder.build(request);
        if (StringUtils.isBlank(request.getName())) {
            stockApplyDO.setName(generateName(request));
        }
        partStockApplyRepositoryService.save(stockApplyDO);
        request.setId(stockApplyDO.getId());
        refPartBatchRepositoryService.saveBatch(PartStockApplyBuilder.buildStockApply(request));
        return stockApplyDO.getId();
    }

    private String generateName(PartStockApplyRequest request) {

        StringBuilder sb = new StringBuilder();


        LambdaQueryWrapper<PartBatchDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PartBatchDO::getDeleted, DeletedEnum.NO.getStatus());
        queryWrapper.in(PartBatchDO::getId, request.getPartBatchs().stream().map(PartStockApplyRequest.PartBatch::getPartBatchId).collect(Collectors.toList()));

        List<PartBatchDO> partBatchDOS = partBatchRepositoryService.list(queryWrapper);
        Map<Long, String> partMap = partBatchDOS.stream().collect(Collectors.toMap(PartBatchDO::getId, PartBatchDO::getName));
        request.getPartBatchs().stream().forEach(o -> {
            sb.append(partMap.get(o.getPartBatchId())).append("(").append(o.getQuantity()).append(")").append("/");
        });
        String name = sb.reverse().replace(0, 1, "").reverse().toString();

        return name;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean update(PartStockApplyRequest request) {

        PartStockApplyDO stockApplyDO = partStockApplyRepositoryService.getById(request.getId());
        AssertUtils.isNotNull(stockApplyDO, PROJECT_PART_STOCK_APPLY_NOT_EXIST);
        AssertUtils.isTrue(PartStockApplyStatusType.WAITING.getCode().equals(stockApplyDO.getStatus()), PROJECT_PART_STOCK_APPLY_STATUS_NOT_VALID);

        stockApplyDO.setUpdatedAt(LocalDateTime.now());
        stockApplyDO.setUpdateBy(request.getOperator());

        if (StringUtils.isBlank(request.getName())) {
            request.setName(generateName(request));
        }

        LambdaUpdateWrapper<PartStockApplyDO> update = updateWrapper();
        update.eq(PartStockApplyDO::getId, request.getId());
        update.set(Objects.nonNull(request.getName()), PartStockApplyDO::getName, request.getName());
        update.set(Objects.nonNull(request.getInvestorType()), PartStockApplyDO::getInvestorType, request.getInvestorType());
//        update.set(Objects.nonNull(request.getOrgId()), PartStockApplyDO::getOrgId, request.getOrgId());
        update.set(Objects.nonNull(request.getInvestor()), PartStockApplyDO::getInvestor, request.getInvestor());
        update.set(Objects.nonNull(request.getRemark()), PartStockApplyDO::getRemark, request.getRemark());
        update.set(Objects.nonNull(request.getAttachments()), PartStockApplyDO::getAttachments, JSONArray.toJSONString(request.getAttachments()));
        update.set(Objects.nonNull(request.getEntryTime()), PartStockApplyDO::getEntryTime, request.getEntryTime());


        update.set(PartStockApplyDO::getUpdateBy, request.getOperator());
        update.set(PartStockApplyDO::getUpdatedAt, LocalDateTime.now());

        partStockApplyRepositoryService.update(update);

        refPartBatchRepositoryService.update2delete(request);
        refPartBatchRepositoryService.saveBatch(PartStockApplyBuilder.buildStockApply(request));
        return true;
    }

    @Override
    public PageResponse<PartStockApplyVO> paging(QueryRequest request) {
        Page<PartStockApplyDO> page = request.ofPage();
        LambdaQueryWrapper<PartStockApplyDO> queryWrapper = queryWrapper();
        queryWrapper.orderByDesc(PartStockApplyDO::getId);
        queryWrapper.eq(Objects.nonNull(request.getCode()), PartStockApplyDO::getCode, request.getCode());
        queryWrapper.like(Objects.nonNull(request.getName()), PartStockApplyDO::getName, request.getName());
        page = partStockApplyRepositoryService.page(page, queryWrapper);
        PageResponse<PartStockApplyVO> pageResponse = PageResponse.build(page, PartStockApplyBuilder::build);
        setAccountName(pageResponse);
        return pageResponse;
    }

    private void setAccountName(PageResponse<PartStockApplyVO> pageResponse) {
        if (CollectionUtils.isEmpty(pageResponse.getRecords())) {
            return;
        }
        Set<Long> accountIds = pageResponse.getRecords().stream().map(o -> o.getUpdateBy()).collect(Collectors.toSet());
        Map<Long, String> nameMap = webAccountService.getAccountMap(accountIds);
        if (CollectionUtils.isEmpty(nameMap)) {
            return;
        }
        pageResponse.getRecords().stream().forEach(o -> {
            o.setOperator(nameMap.get(o.getUpdateBy()));
        });
    }

    @Override
    public PartStockApplyVO detail(Long id) {

        return PartStockApplyBuilder.build(partStockApplyRepositoryService.getById(id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean audit(PartStockApplyAuditRequest request) {
        PartStockApplyDO partStockApplyDO = partStockApplyRepositoryService.getById(request.getId());
        AssertUtils.isNotNull(partStockApplyDO, PROJECT_PART_STOCK_APPLY_NOT_EXIST);
        AssertUtils.isTrue(PartStockApplyStatusType.WAITING.getCode().equals(partStockApplyDO.getStatus()), PROJECT_PART_STOCK_APPLY_STATUS_NOT_VALID);

        LambdaUpdateWrapper<PartStockApplyDO> updateWrapper = updateWrapper();
        updateWrapper.eq(PartStockApplyDO::getId, request.getId());
        updateWrapper.set(PartStockApplyDO::getStatus, request.getStatus());
        updateWrapper.set(PartStockApplyDO::getUpdateBy, request.getOperator());
        updateWrapper.set(PartStockApplyDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.set(PartStockApplyDO::getAuditCompletedTime, LocalDateTime.now());
        updateWrapper.set(PartStockApplyDO::getAuditAccountId, request.getOperator());

        if (PartStockApplyStatusType.PASSED.getCode().equals(request.getStatus())) {
            LocalDateTime entryTime = partStockApplyDO.getEntryTime();
            List<PartStockApplyRefPartBatchDO> batchs = refPartBatchRepositoryService.getPartBatchBy(request.getId());
            for (PartStockApplyRefPartBatchDO partBatch : batchs) {
                partBatchService.entryStock(entryTime, request.getId(), partBatch, request.getOperator());
            }
        }
        return partStockApplyRepositoryService.update(updateWrapper);
    }


    private LambdaQueryWrapper<PartStockApplyDO> queryWrapper() {
        LambdaQueryWrapper<PartStockApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PartStockApplyDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<PartStockApplyDO> updateWrapper() {
        LambdaUpdateWrapper<PartStockApplyDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PartStockApplyDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}

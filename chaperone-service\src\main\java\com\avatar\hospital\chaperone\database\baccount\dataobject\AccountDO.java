package com.avatar.hospital.chaperone.database.baccount.dataobject;

import com.avatar.hospital.chaperone.database.baccount.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.baccount.enums.AccountStatus;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * B端用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("t_b_account")
public class AccountDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 用户头像地址
     */
    private String avatarUrl;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.AccountStatus
     */
    private Integer status;

    /**
     * 用户类型
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.AccountType
     */
    private Integer type;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginDate;

    /**
     * 密码最后更新时间
     */
    private LocalDateTime passwordUpdateDate;

    /**
     * 是否管理员账户
     */
    private Boolean admin;


    /**
     * 院区ID
     */
    private Long hid;

    /**
     * 判断是否管理员账号
     *
     * @return -
     */
    public boolean admin() {
        return Boolean.TRUE.equals(admin);
    }

    /**
     * 判断状态是否启用
     *
     * @return -
     */
    public boolean enable() {
        return AccountStatus.ENABLE.getStatus().equals(status);
    }
}

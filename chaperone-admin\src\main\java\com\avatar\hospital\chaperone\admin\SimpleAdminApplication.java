package com.avatar.hospital.chaperone.admin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 简化版超级管理员应用启动类
 * 用于测试基础启动功能，排除数据库配置
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class SimpleAdminApplication {

    public static void main(String[] args) {
        System.setProperty("spring.profiles.active", "simple");
        SpringApplication.run(SimpleAdminApplication.class, args);
    }

}

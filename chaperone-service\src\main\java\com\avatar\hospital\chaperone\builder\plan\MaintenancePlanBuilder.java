package com.avatar.hospital.chaperone.builder.plan;

import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.avatar.hospital.chaperone.database.plan.dataobject.*;
import com.avatar.hospital.chaperone.database.plan.enums.CircleType;
import com.avatar.hospital.chaperone.database.plan.enums.PlanStatusType;
import com.avatar.hospital.chaperone.database.plan.enums.TaskStatusType;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.response.plan.PlanVO;
import com.avatar.hospital.chaperone.utils.CodeUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 10:52
 */
public class MaintenancePlanBuilder {

    public static PlanVO build(MaintenancePlanDO MaintenancePlanDO) {
        PlanVO planVO = new PlanVO();
        if (Objects.isNull(MaintenancePlanDO)) {
            return null;
        }
        BeanUtils.copyProperties(MaintenancePlanDO, planVO);
        return planVO;
    }

    public static MaintenancePlanDO build(PlanRequest request) {
        MaintenancePlanDO planDO = new MaintenancePlanDO();
        BeanUtils.copyProperties(request, planDO);
        planDO.setUpdateBy(request.getOperator());
        if (Objects.nonNull(request.getId())) {
            return planDO;
        }
        planDO.setHid(request.getHospitalId());
        planDO.setStatus(PlanStatusType.VALID.getCode());
        planDO.setCode(CodeUtil.generateCode(CodeBizType.XJJH));
        planDO.setCreateBy(request.getOperator());
        return planDO;
    }

    public static List<MaintenancePlanRefDeviceDO> buildRefDevice(PlanRequest request) {
        List<MaintenancePlanRefDeviceDO> refDeviceDOS = request.getDeviceIds().stream().map(o -> {
            MaintenancePlanRefDeviceDO refDeviceDO = new MaintenancePlanRefDeviceDO();
            refDeviceDO.setDeviceId(o);
            refDeviceDO.setHid(request.getHospitalId());
            refDeviceDO.setPlanId(request.getId());
            refDeviceDO.setCreateBy(request.getOperator());
            refDeviceDO.setUpdateBy(request.getOperator());
            return refDeviceDO;
        }).collect(Collectors.toList());
        return refDeviceDOS;
    }

    public static List<MaintenancePlanRefOrgDO> buildRefOrg(PlanRequest request) {
        List<MaintenancePlanRefOrgDO> refOrgDOS = request.getOrgIds().stream().map(o -> {
            MaintenancePlanRefOrgDO refOrgDO = new MaintenancePlanRefOrgDO();
            refOrgDO.setOrgId(o);
            refOrgDO.setHid(request.getHospitalId());
            refOrgDO.setPlanId(request.getId());
            refOrgDO.setCreateBy(request.getOperator());
            refOrgDO.setUpdateBy(request.getOperator());
            return refOrgDO;
        }).collect(Collectors.toList());
        return refOrgDOS;
    }

    public static List<MaintenancePlanRefExecutorDO> buildRefExecutor(PlanRequest request) {
        List<MaintenancePlanRefExecutorDO> refExecutorDOS = request.getExecutorIds().stream().map(o -> {
            MaintenancePlanRefExecutorDO refExecutorDO = new MaintenancePlanRefExecutorDO();
            refExecutorDO.setExecutorAccountId(o);
            refExecutorDO.setPlanId(request.getId());
            refExecutorDO.setHid(request.getHospitalId());
            refExecutorDO.setCreateBy(request.getOperator());
            refExecutorDO.setUpdateBy(request.getOperator());
            return refExecutorDO;
        }).collect(Collectors.toList());
        return refExecutorDOS;
    }

    public static List<MaintenancePlanTaskDO> buildTask(MaintenancePlanDO MaintenancePlanDO, List<Long> deviceIds) {
        List<MaintenancePlanTaskDO> taskDOS = Lists.newArrayListWithCapacity(deviceIds.size());
        for (int i = 0; i < deviceIds.size(); i++) {
            MaintenancePlanTaskDO planTaskDO = new MaintenancePlanTaskDO();
            taskDOS.add(planTaskDO);
            planTaskDO.setCode(CodeUtil.generateCode(CodeBizType.WBRW));
            planTaskDO.setDeviceId(deviceIds.get(i));
            planTaskDO.setPlanId(MaintenancePlanDO.getId());
            planTaskDO.setName(MaintenancePlanTaskDO.generateName(MaintenancePlanDO.getName(), CircleType.of(MaintenancePlanDO.getCircleType()), i + 1));
            planTaskDO.setStatus(TaskStatusType.NON_COMPLETED.getCode());
            planTaskDO.setCreateBy(MaintenancePlanDO.getUpdateBy());
            planTaskDO.setUpdateBy(MaintenancePlanDO.getUpdateBy());
        }
        return taskDOS;
    }
}

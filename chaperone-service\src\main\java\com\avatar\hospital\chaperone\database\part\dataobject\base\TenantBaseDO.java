package com.avatar.hospital.chaperone.database.part.dataobject.base;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Setter
@Getter
public class TenantBaseDO implements Serializable {

    private static final long serialVersionUID = 6791133411753063011L;

    public TenantBaseDO() {
        createInit();
    }

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 院区ID
     */
    private Long hid;

    /**
     * 删除时间 1:未删除;已删除:时间戳
     */
    @TableField(select = false)
    private Long deleted;

    private void createInit() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        deleted = Long.valueOf(DeletedEnum.NO.getStatus());
    }

    public void updateInit() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
}

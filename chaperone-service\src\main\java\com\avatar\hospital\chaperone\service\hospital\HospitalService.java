package com.avatar.hospital.chaperone.service.hospital;

import com.avatar.hospital.chaperone.request.hospital.HospitalAddRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalDeleteRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalPagingRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.hospital.*;

import java.util.List;

public interface HospitalService {

    HospitalAddResponse add(HospitalAddRequest request);

    HospitalUpdateResponse update(HospitalUpdateRequest request);

    PageResponse<HospitalPagingResponse> paging(HospitalPagingRequest request);

    HospitalDeleteResponse delete(HospitalDeleteRequest request);

    List<HospitalResourceDataResponse> getResourceData();
}

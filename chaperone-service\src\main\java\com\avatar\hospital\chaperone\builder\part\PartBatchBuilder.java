package com.avatar.hospital.chaperone.builder.part;

import com.avatar.hospital.chaperone.context.UserContext;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.enums.PartBatchStatusType;
import com.avatar.hospital.chaperone.database.part.enums.PartStatusType;
import com.avatar.hospital.chaperone.database.part.repository.PartStockApplyRepositoryService;
import com.avatar.hospital.chaperone.request.part.PartBatchRequest;
import com.avatar.hospital.chaperone.response.part.PartBatchVO;
import com.avatar.hospital.chaperone.response.part.PartVO;
import com.avatar.hospital.chaperone.service.part.PartBatchService;
import com.avatar.hospital.chaperone.service.part.PartStockApplyService;
import com.avatar.hospital.chaperone.utils.CodeUtil;
import com.avatar.hospital.chaperone.utils.SpringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 13:22
 */
public class PartBatchBuilder {


    public static PartBatchDO buildUpdate(PartBatchRequest partBatchRequest) {
        PartBatchDO partBatchDO = new PartBatchDO();
        BeanUtils.copyProperties(partBatchRequest, partBatchDO);
        partBatchDO.setUpdateBy(partBatchRequest.getOperator());
        partBatchDO.setUpdatedAt(LocalDateTime.now());
        return partBatchDO;
    }

    public static PartBatchDO build(PartBatchRequest partBatchRequest) {
        PartBatchDO partBatchDO = new PartBatchDO();
        BeanUtils.copyProperties(partBatchRequest, partBatchDO);
        partBatchDO.setCode(CodeUtil.generateCode(CodeBizType.BJ));
        partBatchDO.setBalance(0);
        partBatchDO.setQuantity(0);
        partBatchDO.setStatus(PartBatchStatusType.NON_STOCK.getCode());
        partBatchDO.setCreateBy(partBatchRequest.getOperator());
        partBatchDO.setUpdateBy(partBatchRequest.getOperator());

        return partBatchDO;
    }

    public static List<PartDO> build(Long stockApplyId, LocalDateTime entryTime, PartBatchDO partBatchDO,
                                     PartStockApplyRefPartBatchDO applyRefPartBatchDO, Long operator) {
        if (Objects.isNull(applyRefPartBatchDO)) {
            return Lists.newArrayList();
        }
        List<PartDO> partDOS = Lists.newArrayList();

        for (int i = 0; i < applyRefPartBatchDO.getQuantity(); i++) {

            Integer num = partBatchDO.getQuantity() + i + 1;
            PartDO part = new PartDO();
            partDOS.add(part);
            StringBuilder sb = new StringBuilder(partBatchDO.getCode());
            sb.append("-").append(num);
            part.setCode(sb.toString());
            part.setStockApplyId(stockApplyId);
            part.setEntryTime(entryTime);
            part.setStatus(PartStatusType.STOCK.getCode());
            part.setCreateBy(operator);
            part.setUpdateBy(operator);
            part.setSparePartBatchId(partBatchDO.getId());
            part.setHid(UserContext.getHospitalId());
        }
        return partDOS;
    }

    public static PartBatchVO buildVO(PartBatchDO partBatchDO) {
        if (Objects.isNull(partBatchDO)) {
            return null;
        }
        PartBatchVO partBatch = new PartBatchVO();
        BeanUtils.copyProperties(partBatchDO, partBatch);
        return partBatch;
    }

    public static PartVO buildVO(PartDO partDO) {
        if (Objects.isNull(partDO)) {
            return null;
        }
        PartVO partVO = new PartVO();
        BeanUtils.copyProperties(partDO, partVO);
        return partVO;

    }

    public static void setPartValue(List<PartVO> partVOS) {
        if (CollectionUtils.isEmpty(partVOS)) {
            return;
        }
        PartBatchVO partBatchVO = SpringUtils.getBean(PartBatchService.class).detail(partVOS.get(0).getSparePartBatchId());
        partVOS.stream().forEach(o -> {
            o.setBatchCode(partBatchVO.getCode());
            o.setName(partBatchVO.getName());
            o.setPrice(partBatchVO.getPrice());
        });

    }

    public static void setPartStockValue(List<PartVO> partVOS) {
        if (CollectionUtils.isEmpty(partVOS)) {
            return;
        }
        Set<Long> stockIds = partVOS.stream().map(PartVO::getStockApplyId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(stockIds)) {
            return;
        }
        LambdaQueryWrapper<PartStockApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PartStockApplyDO::getId, stockIds);
        queryWrapper.eq(PartStockApplyDO::getDeleted, DeletedEnum.NO.getStatus());

        List<PartStockApplyDO> stockApplyDOS = SpringUtils.getBean(PartStockApplyRepositoryService.class).list(queryWrapper);
        if (CollectionUtils.isEmpty(stockApplyDOS)) {
            return;
        }
        Map<Long, PartStockApplyDO> stockApplyDOMap = stockApplyDOS.stream().collect(Collectors.toMap(PartStockApplyDO::getId, Function.identity()));

        partVOS.stream().forEach(o -> {
            PartStockApplyDO stockApplyDO = stockApplyDOMap.get(o.getStockApplyId());
            if (Objects.nonNull(stockApplyDO)) {
                o.setInvestorType(stockApplyDO.getInvestorType());
                o.setInvestorOrgId(stockApplyDO.getOrgId());
                o.setInvestor(stockApplyDO.getInvestor());
                o.setStockApplyCode(stockApplyDO.getCode());
            }
        });
    }
}

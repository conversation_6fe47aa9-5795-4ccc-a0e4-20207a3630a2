package com.avatar.hospital.chaperone.web.controller.device;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.request.device.DeviceAddRequest;
import com.avatar.hospital.chaperone.request.device.DevicePagingRequest;
import com.avatar.hospital.chaperone.request.device.DeviceUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.device.DeviceAddResponse;
import com.avatar.hospital.chaperone.response.device.DevicePagingResponse;
import com.avatar.hospital.chaperone.response.device.DeviceUpdateResponse;
import com.avatar.hospital.chaperone.service.device.DeviceService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.utils.QrUtils;
import com.avatar.hospital.chaperone.web.validator.device.DeviceValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @author:sp0420
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/project/device")
public class ProjectDeviceController {

    private final DeviceService deviceService;

    /**
     * 创建
     */
    @PostMapping(value = "")
    public SingleResponse<DeviceAddResponse> add(@RequestBody DeviceAddRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("ProjectDeviceController add request:{}", request);
            DeviceValidator.addValidate(request);
            return deviceService.add(request);
        });
    }

    /**
     * 修改
     */
    @PutMapping(value = "")
    public SingleResponse<DeviceUpdateResponse> update(@RequestBody DeviceUpdateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("ProjectDeviceController update request:{}", request);
            DeviceValidator.updateValidate(request);
            return deviceService.update(request);
        });
    }


    /**
     * 分页
     */
    @GetMapping(value = "/paging")
    public SingleResponse<PageResponse<DevicePagingResponse>> paging(DevicePagingRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("ProjectDeviceController paging request:{}", request);
            DeviceValidator.pagingValidate(request);
            return deviceService.paging(request);
        });
    }

    /**
     * 生成二维码，根据id
     */
    @GetMapping(value = "/qrCode")
    public void qrCode(HttpServletResponse response, String content) throws IOException {
        QrUtils.createCodeToOutputStream(content, response.getOutputStream());
    }

}

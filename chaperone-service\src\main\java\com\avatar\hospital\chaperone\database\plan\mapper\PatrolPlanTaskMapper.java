package com.avatar.hospital.chaperone.database.plan.mapper;

import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanTaskDO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 巡检计划任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PatrolPlanTaskMapper extends BaseMapper<PatrolPlanTaskDO> {

    @InterceptorIgnore(tenantLine = "true")
    @Select("SELECT * FROM t_project_patrol_plan_task ${ew.customSqlSegment}")
    List<PatrolPlanTaskDO> listWithoutTenant(@Param("ew") LambdaQueryWrapper<PatrolPlanTaskDO> queryWrapper);

}

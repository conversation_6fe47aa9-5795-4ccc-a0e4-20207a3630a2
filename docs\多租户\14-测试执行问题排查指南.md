# 测试执行问题排查指南

## 🔍 问题现象

在执行 `PatrolPlanRepositoryServiceTest.testSelectAllPlan()` 测试方法时出现报错，无法正常获取计划数据。

## 🔧 已修复的问题

### 1. 参数传递错误 ✅

**问题**: `PatrolPlanRepositoryServiceImpl.selectAllWithoutTenant()` 方法忽略了传入的查询参数

```java
// ❌ 修复前
@Override
public List<PatrolPlanDO> selectAllWithoutTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper) {
    return baseMapper.selectAllWithoutTenant(queryWrapper()); // 忽略传入参数
}

// ✅ 修复后
@Override
public List<PatrolPlanDO> selectAllWithoutTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper) {
    return baseMapper.selectAllWithoutTenant(queryWrapper); // 使用传入参数
}
```

### 2. 数据类型不一致 ✅

**问题**: `TenantBaseDO` 中的 `hid` 字段类型不一致

```java
// ❌ 修复前
private long hid; // 基本类型

// ✅ 修复后  
private Long hid; // 包装类型
```

### 3. 删除标记值错误 ✅

**问题**: 测试中使用了错误的删除标记值

```java
// ❌ 修复前
queryWrapper.eq(PatrolPlanDO::getDeleted, 1L); // 错误：1表示已删除

// ✅ 修复后
queryWrapper.eq(PatrolPlanDO::getDeleted, 0L); // 正确：0表示未删除
```

## 🧪 测试改进

### 增强的测试方法

```java
@Test
public void testSelectAllPlan() {
    try {
        System.out.println("开始测试 selectAllWithoutTenant 方法...");
        
        // 创建查询条件
        LambdaQueryWrapper<PatrolPlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatrolPlanDO::getStatus, PlanStatusType.VALID.getCode());
        queryWrapper.eq(PatrolPlanDO::getDeleted, 0L); // 0表示未删除
        
        System.out.println("查询条件创建完成，开始执行查询...");
        
        // 执行查询
        List<PatrolPlanDO> list = repositoryService.selectAllWithoutTenant(queryWrapper);
        
        System.out.println("查询完成，结果数量: " + (list != null ? list.size() : "null"));
        
        if (list != null) {
            for (PatrolPlanDO planDO : list) {
                System.out.println("计划ID: " + planDO.getId() + 
                                 ", 名称: " + planDO.getName() + 
                                 ", 状态: " + planDO.getStatus() + 
                                 ", 院区ID: " + planDO.getHid());
            }
        }
        
        System.out.println("测试完成！");
        
    } catch (Exception e) {
        System.err.println("测试执行出错: " + e.getMessage());
        e.printStackTrace();
        throw e;
    }
}
```

### 基本功能测试

```java
@Test
public void testBasicQuery() {
    try {
        System.out.println("开始测试基本查询功能...");
        
        // 测试普通查询（会应用租户隔离）
        LambdaQueryWrapper<PatrolPlanDO> normalWrapper = new LambdaQueryWrapper<>();
        normalWrapper.eq(PatrolPlanDO::getDeleted, 0L);
        
        List<PatrolPlanDO> normalList = repositoryService.list(normalWrapper);
        System.out.println("普通查询结果数量: " + (normalList != null ? normalList.size() : "null"));
        
        // 测试忽略租户隔离的查询
        List<PatrolPlanDO> allList = repositoryService.selectAllWithoutTenant(normalWrapper);
        System.out.println("忽略租户隔离查询结果数量: " + (allList != null ? allList.size() : "null"));
        
        System.out.println("基本查询测试完成！");
        
    } catch (Exception e) {
        System.err.println("基本查询测试出错: " + e.getMessage());
        e.printStackTrace();
        throw e;
    }
}
```

## 🔍 可能的其他问题

### 1. 数据库连接问题

**检查项**:
- 数据库是否正常运行
- 连接配置是否正确
- 数据库中是否有测试数据

**配置文件**: `chaperone-service/src/test/resources/config/application-dev.yaml`

```yaml
spring:
  datasource:
    url: jdbc:mysql://${db.mysql}:3306/chaperone?useSSL=false&useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: root
    password: 123456
```

### 2. 多租户插件配置问题

**检查项**:
- `TenantHandler` 是否正确注册为Spring Bean
- `MybatisPlusConfig` 是否正确配置租户插件
- `@InterceptorIgnore` 注解是否在正确位置

### 3. 实体类映射问题

**检查项**:
- `PatrolPlanDO` 继承关系是否正确
- `TenantBaseDO` 中的字段是否与数据库表结构匹配
- `@TableName` 注解是否正确

## 🛠️ 排查步骤

### 步骤1: 验证数据库连接

```java
@Test
public void testDatabaseConnection() {
    try {
        // 简单查询测试数据库连接
        long count = repositoryService.count();
        System.out.println("数据库连接正常，总记录数: " + count);
    } catch (Exception e) {
        System.err.println("数据库连接失败: " + e.getMessage());
        throw e;
    }
}
```

### 步骤2: 验证租户插件配置

```java
@Test
public void testTenantPlugin() {
    try {
        // 测试租户插件是否正常工作
        LambdaQueryWrapper<PatrolPlanDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PatrolPlanDO::getDeleted, 0L);
        
        // 这个查询应该被租户插件拦截
        List<PatrolPlanDO> list = repositoryService.list(wrapper);
        System.out.println("租户插件测试完成，结果数量: " + list.size());
    } catch (Exception e) {
        System.err.println("租户插件测试失败: " + e.getMessage());
        throw e;
    }
}
```

### 步骤3: 验证忽略租户隔离功能

```java
@Test
public void testIgnoreTenant() {
    try {
        LambdaQueryWrapper<PatrolPlanDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PatrolPlanDO::getDeleted, 0L);
        
        // 这个查询应该忽略租户隔离
        List<PatrolPlanDO> list = repositoryService.selectAllWithoutTenant(wrapper);
        System.out.println("忽略租户隔离测试完成，结果数量: " + list.size());
    } catch (Exception e) {
        System.err.println("忽略租户隔离测试失败: " + e.getMessage());
        throw e;
    }
}
```

## 📝 常见错误和解决方案

### 错误1: ClassNotFoundException

**原因**: 缺少依赖或类路径问题
**解决**: 检查Maven依赖，确保所有必要的jar包都已正确引入

### 错误2: SQLException

**原因**: 数据库连接问题或SQL语法错误
**解决**: 检查数据库配置，查看生成的SQL语句

### 错误3: BeanCreationException

**原因**: Spring Bean创建失败
**解决**: 检查组件扫描配置，确保所有必要的Bean都能被正确创建

### 错误4: MyBatis映射错误

**原因**: 实体类与数据库表结构不匹配
**解决**: 检查实体类字段和数据库表字段的对应关系

## 🎯 验证清单

- [ ] 数据库连接正常
- [ ] 测试数据存在
- [ ] 实体类映射正确
- [ ] 租户插件配置正确
- [ ] `@InterceptorIgnore` 注解位置正确
- [ ] 查询条件正确（特别是删除标记）
- [ ] 参数传递正确

## 📖 相关文档

- `13-计划查询问题修复总结.md` - 参数传递问题修复
- `11-InterceptorIgnore注解问题解决方案.md` - 租户隔离忽略方案
- `01-最终实施状态总结.md` - 多租户整体实施状态

通过以上排查步骤和修复措施，测试方法应该能够正常执行！

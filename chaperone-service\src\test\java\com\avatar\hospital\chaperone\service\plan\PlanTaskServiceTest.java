package com.avatar.hospital.chaperone.service.plan;

import com.avatar.hospital.chaperone.TestApplication;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.response.plan.PlanTaskVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;


@SpringBootTest(classes = TestApplication.class)
class PlanTaskServiceTest {

    @Autowired
    private PlanTaskService planTaskService;

    @Test
    public void testExecute() {
    }

    // 巡检
    @Test
    void getPlanBeforeTodayTaskWithoutTenant() {
        List<PlanTaskVO> list = planTaskService.getPlanBeforeTodayTaskWithoutTenant(PlanType.PATROL, 4L);
        System.out.println(list);
    }
}
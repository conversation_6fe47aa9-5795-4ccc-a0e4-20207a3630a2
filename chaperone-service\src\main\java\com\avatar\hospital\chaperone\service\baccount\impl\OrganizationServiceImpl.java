package com.avatar.hospital.chaperone.service.baccount.impl;

import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.builder.baccount.OrganizationBuilder;
import com.avatar.hospital.chaperone.component.baccount.OrganizationComponent;
import com.avatar.hospital.chaperone.component.baccount.RoleComponent;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.enums.OrganizationLevel;
import com.avatar.hospital.chaperone.database.baccount.enums.OrganizationStatus;
import com.avatar.hospital.chaperone.database.baccount.enums.OrganizationType;
import com.avatar.hospital.chaperone.database.baccount.repository.OrganizationRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.WebAccountRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.OrganizationAddRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationHospitalRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationTreeRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationUpdateRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAdministrativeOfficeQueryRequest;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAdministrativeOfficeQueryRequest;
import com.avatar.hospital.chaperone.response.baccount.OrganizationAddResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationHospitalResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationTreeResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationUpdateResponse;
import com.avatar.hospital.chaperone.response.caccount.ConsumerOrganizationHospitalResponse;
import com.avatar.hospital.chaperone.service.baccount.OrganizationService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrganizationServiceImpl implements OrganizationService {

    private final OrganizationRepositoryService organizationRepositoryService;
    private final WebAccountRepositoryService webAccountRepositoryService;
    private final OrganizationComponent organizationComponent;
    private final RoleComponent roleComponent;

    @Override
    public OrganizationAddResponse add(OrganizationAddRequest request) {
        OrganizationDO organizationDO;

        // 设置默认值
        if (request.getParentId() == null) {
            request.setParentId(0L);
        } else {
            organizationDO = organizationRepositoryService.findById(request.getParentId());
            AssertUtils.isNotNull(organizationDO, ErrorCode.ORGANIZATION_PARENT_NOT_EXIST);
            AssertUtils.isTrue(OrganizationStatus.ENABLE.getStatus().equals(organizationDO.getStatus()), ErrorCode.ORGANIZATION_PARENT_STATUS_NOT_ENABLED);
        }

        Long organizationId = organizationRepositoryService.add(OrganizationBuilder.buildAddOrganizationDO(request));
        return OrganizationAddResponse.builder().organizationId(organizationId).build();
    }

    @Override
    public OrganizationUpdateResponse update(OrganizationUpdateRequest request) {
        OrganizationDO organizationDO = organizationRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(organizationDO, ErrorCode.ORGANIZATION_NOT_EXIST);
        Boolean result = organizationRepositoryService.incrementUpdate(OrganizationBuilder.buildOrganizationDO(request));
        AssertUtils.isTrue(result, ErrorCode.UPDATE_ERROR);
        return OrganizationUpdateResponse.builder().success(true).build();
    }

    @Override
    public OrganizationDeleteResponse delete(OrganizationDeleteRequest request) {
        List<OrganizationDO> organizationList = organizationRepositoryService.findByIds(request.getIds());
        if (CollectionUtils.isEmpty(organizationList)) {
            return OrganizationDeleteResponse.builder().success(true).build();
        }
        Set<Long> organizationIds = organizationList.stream().map(OrganizationDO::getId).collect(Collectors.toSet());
        Boolean result = organizationRepositoryService.deleteByIds(organizationIds, request.getUpdateBy());
        return OrganizationDeleteResponse.builder().success(result).build();
    }

    @Override
    public List<OrganizationTreeResponse> getTree() {
        // 查询所有数据
        List<OrganizationDO> organizationList = organizationRepositoryService.listAll();

        // 组装成树形结构
        // 1. 将DO转换为Response对象
        List<OrganizationTreeResponse> organizationTreeResponseList = OrganizationBuilder.buildOrganizationTreeResponseList(organizationList);

        // 2. 构建树形结构（parent_id = 1 表示顶层）
        return buildOrganizationTreeWithCustomRoot(organizationTreeResponseList, 0L);
    }

    /**
     * 构建组织机构树形结构（支持自定义根节点parentId）
     *
     * @param organizationTreeResponseList 组织机构列表
     * @param rootParentId                 根节点的parentId值
     * @return 树形结构列表
     */
    private List<OrganizationTreeResponse> buildOrganizationTreeWithCustomRoot(
            List<OrganizationTreeResponse> organizationTreeResponseList, Long rootParentId) {
        if (CollectionUtils.isEmpty(organizationTreeResponseList)) {
            return Collections.emptyList();
        }

        List<OrganizationTreeResponse> resultList = Lists.newLinkedList();

        // 构建父子关系映射
        for (OrganizationTreeResponse parent : organizationTreeResponseList) {
            // 添加顶级节点（parent_id = rootParentId 的节点）
            if (Objects.equals(parent.getParentId(), rootParentId)) {
                resultList.add(parent);
            }

            // 为每个节点查找子节点
            for (OrganizationTreeResponse child : organizationTreeResponseList) {
                if (Objects.equals(child.getParentId(), parent.getId())) {
                    if (CollectionUtils.isEmpty(parent.getChildrenList())) {
                        parent.setChildrenList(Lists.newLinkedList());
                    }
                    parent.getChildrenList().add(child);
                }
            }
        }

        return resultList;
    }


    @Override
    public List<OrganizationTreeResponse> accountTree(OrganizationTreeRequest request) {
        // 管理员账户返回所有组织机构
        AccountDO accountDO = webAccountRepositoryService.findById(request.getAccountId());
        if (accountDO != null && accountDO.admin()) {
            return tree(request);
        }
        // 如果有查询组织机构树的角色,返回所有组织机构
        if (roleComponent.isSelectOrganizationTreeRole(request.getAccountId())) {
            return tree(request);
        }

        List<OrganizationDO> hospitalOrganizationList = organizationComponent.findHospitalAndDepartmentOrganizationList(organizationComponent.findAccountOrganizationIds(request.getAccountId()));
        if (CollectionUtils.isEmpty(hospitalOrganizationList)) {
            log.warn("OrganizationServiceImpl accountTree hospitalOrganizationList empty accountId:{}", request.getAccountId());
            return Collections.emptyList();
        }

        List<OrganizationDO> organizationList = Lists.newLinkedList(hospitalOrganizationList);
        Set<Long> organizationIds = hospitalOrganizationList.stream().map(OrganizationDO::getId).collect(Collectors.toSet());
        // 递归查询医院级别下面所有的组织机构
        organizationRecursionFindChildren(organizationIds, organizationList);
        // 设置子节点
        return OrganizationBuilder.buildOrganizationTreeChildrenList(OrganizationBuilder.buildOrganizationTreeResponseList(organizationList), organizationIds);
    }

    public List<OrganizationTreeResponse> tree(OrganizationTreeRequest request) {
        log.info("OrganizationServiceImpl tree request:{}", JSON.toJSONString(request));
        List<OrganizationDO> organizationList = organizationRepositoryService.listAll();
        // 设置子节点
        return OrganizationBuilder.buildOrganizationTreeChildrenList(OrganizationBuilder.buildOrganizationTreeResponseList(organizationList));
    }

    @Override
    public List<OrganizationHospitalResponse> hospitalOrganizationList(OrganizationHospitalRequest request) {
        // 管理员返回所有医院层级
        AccountDO accountDO = webAccountRepositoryService.findById(request.getAccountId());
        if (accountDO != null && accountDO.admin()) {
            List<OrganizationDO> organizationList = organizationComponent.findAllHospitalOrganizationList();
            return OrganizationBuilder.builderOrganizationHospitalResponseList(organizationList);
        }
        // 如果有查看所有医院层级的角色,返回所有的医院层级列表
        if (roleComponent.isSelectHospitalListRole(request.getAccountId())) {
            List<OrganizationDO> organizationList = organizationComponent.findAllHospitalOrganizationList();
            return OrganizationBuilder.builderOrganizationHospitalResponseList(organizationList);
        }

        List<OrganizationDO> organizationList = organizationComponent.findHospitalOrganizationList(organizationComponent.findAccountOrganizationIds(request.getAccountId()));
        if (CollectionUtils.isEmpty(organizationList)) {
            log.warn("OrganizationServiceImpl hospitalOrganizationList organizationList empty accountId:{}", request.getAccountId());
            return Collections.emptyList();
        }

        return OrganizationBuilder.builderOrganizationHospitalResponseList(organizationList);
    }

    @Override
    public List<OrganizationHospitalResponse> departmentOrganizationList(OrganizationHospitalRequest request) {
        // 管理员返回所有医院层级
        AccountDO accountDO = webAccountRepositoryService.findById(request.getAccountId());
        if (accountDO != null && accountDO.admin()) {
            List<OrganizationDO> organizationList = organizationComponent.findAllDepartmentOrganizationList();
            return OrganizationBuilder.builderOrganizationHospitalResponseList(organizationList);
        }
        // 如果有查看所有部门层级的角色,返回所有的部门层级列表
        if (roleComponent.isSelectDepartmentListRole(request.getAccountId())) {
            List<OrganizationDO> organizationList = organizationComponent.findAllDepartmentOrganizationList();
            return OrganizationBuilder.builderOrganizationHospitalResponseList(organizationList);
        }

        List<OrganizationDO> organizationList = organizationComponent.findDepartmentOrganizationList(organizationComponent.findAccountOrganizationIds(request.getAccountId()));
        if (CollectionUtils.isEmpty(organizationList)) {
            log.warn("OrganizationServiceImpl departmentOrganizationList organizationList empty accountId:{}", request.getAccountId());
            return Collections.emptyList();
        }

        return OrganizationBuilder.builderOrganizationHospitalResponseList(organizationList);
    }

    @Override
    public List<OrganizationHospitalResponse> webAdministrativeOfficeOrganizationList(WebAdministrativeOfficeQueryRequest request) {
        OrganizationDO organizationDO = organizationRepositoryService.findById(request.getHospitalOrganizationId());
        AssertUtils.isNotNull(organizationDO, ErrorCode.HOSPITAL_NOT_EXIST);

        List<OrganizationDO> organizationList = organizationComponent.findWebAdministrativeOfficeOrganizationList(organizationDO.getId());
        return OrganizationBuilder.builderOrganizationHospitalResponseList(organizationList);
    }

    @Override
    public List<ConsumerOrganizationHospitalResponse> consumerHospitalOrganizationList() {
        List<OrganizationDO> organizationList = organizationComponent.findAllHospitalOrganizationList();
        return OrganizationBuilder.builderConsumerOrganizationHospitalResponseList(organizationList);
    }

    @Override
    public List<ConsumerOrganizationHospitalResponse> consumerAdministrativeOfficeOrganizationList(ConsumerAdministrativeOfficeQueryRequest request) {
        OrganizationDO organizationDO = organizationRepositoryService.findById(request.getHospitalOrganizationId());
        AssertUtils.isNotNull(organizationDO, ErrorCode.HOSPITAL_NOT_EXIST);

        List<OrganizationDO> organizationList = organizationComponent.findConsumerAdministrativeOfficeOrganizationList(organizationDO.getId());
        return OrganizationBuilder.builderConsumerOrganizationHospitalResponseList(organizationList);
    }

    /**
     * 递归查询子节点
     *
     * @param organizationIds  -
     * @param organizationList -
     */
    private void organizationRecursionFindChildren(Set<Long> organizationIds, List<OrganizationDO> organizationList) {
        if (CollectionUtils.isEmpty(organizationIds)) {
            return;
        }
        List<OrganizationDO> childrenOrganizationList = organizationRepositoryService.findByParentId(organizationIds);
        if (CollectionUtils.isEmpty(childrenOrganizationList)) {
            return;
        }
        organizationList.addAll(childrenOrganizationList);
        Set<Long> ids = childrenOrganizationList.stream().map(OrganizationDO::getId).collect(Collectors.toSet());
        organizationRecursionFindChildren(ids, organizationList);
    }
}

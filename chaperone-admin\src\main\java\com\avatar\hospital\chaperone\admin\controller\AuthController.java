package com.avatar.hospital.chaperone.admin.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.avatar.hospital.chaperone.admin.common.Result;
import com.avatar.hospital.chaperone.admin.dto.LoginRequest;
import com.avatar.hospital.chaperone.admin.entity.AdminUser;
import com.avatar.hospital.chaperone.admin.service.AdminUserService;
import com.avatar.hospital.chaperone.admin.vo.LoginResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private AdminUserService adminUserService;

    /**
     * 登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request, HttpServletRequest httpRequest) {
        // 查询用户
        AdminUser user = adminUserService.getByUsername(request.getUsername());
        if (user == null) {
            return Result.badRequest("用户名或密码错误");
        }

        // 检查用户状态
        if (!AdminUser.Status.ENABLED.getCode().equals(user.getStatus())) {
            return Result.badRequest("用户已被禁用");
        }

        // 验证密码
        if (!adminUserService.verifyPassword(request.getPassword(), user.getPassword())) {
            return Result.badRequest("用户名或密码错误");
        }

        // 执行登录
        StpUtil.login(user.getId());

        // 更新登录信息
        String clientIp = getClientIp(httpRequest);
        adminUserService.updateLastLoginInfo(user.getId(), clientIp);

        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setToken(StpUtil.getTokenValue());
        response.setTokenType("Bearer");
        response.setExpiresIn(StpUtil.getTokenTimeout());

        // 用户信息
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setRealName(user.getRealName());
        userInfo.setPhone(user.getPhone());
        userInfo.setEmail(user.getEmail());
        userInfo.setAvatar(user.getAvatar());
        userInfo.setRole(user.getRole());
        userInfo.setPermissionScope(user.getPermissionScope());
        userInfo.setLastLoginTime(user.getLastLoginTime());
        response.setUserInfo(userInfo);

        return Result.success("登录成功", response);
    }

    /**
     * 登出
     */
    @PostMapping("/logout")
    public Result<String> logout() {
        StpUtil.logout();
        return Result.success("登出成功");
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/userinfo")
    public Result<LoginResponse.UserInfo> getUserInfo() {
        Long userId = StpUtil.getLoginIdAsLong();
        AdminUser user = adminUserService.getById(userId);
        if (user == null) {
            return Result.notFound("用户不存在");
        }

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setRealName(user.getRealName());
        userInfo.setPhone(user.getPhone());
        userInfo.setEmail(user.getEmail());
        userInfo.setAvatar(user.getAvatar());
        userInfo.setRole(user.getRole());
        userInfo.setPermissionScope(user.getPermissionScope());
        userInfo.setLastLoginTime(user.getLastLoginTime());

        return Result.success(userInfo);
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

}

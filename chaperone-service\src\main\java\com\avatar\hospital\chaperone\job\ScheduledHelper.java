package com.avatar.hospital.chaperone.job;


import com.avatar.hospital.chaperone.database.order.dataobject.ScheduledLogDO;
import com.avatar.hospital.chaperone.annotation.TraceLog;
import com.avatar.hospital.chaperone.database.order.repository.ScheduledLogRepositoryService;
import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;


/**
 * 定时任务执行
 *
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-18 09:13
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class ScheduledHelper {

    private final RedissonClient redissonClient;

    @Resource(name = "scheduledHelperExecutor")
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private final ScheduledLogRepositoryService scheduledLogRepositoryService;


    @TraceLog
    public <T> void exec(ScheduledType scheduledType, Supplier<T> supplier) {
        log.info("ScheduledHelper 定时任务 start {}", scheduledType.toString());
        ScheduledLogDO logDO = scheduledLogRepositoryService.create(scheduledType);
        long start = System.currentTimeMillis();
        RLock lock = redissonClient.getLock(scheduledType.getLockKey());
        try {
            if (!lock.tryLock(3, TimeUnit.MINUTES)) {
                log.info("ScheduledHelper[]定时任务 finish lock {}", scheduledType.toString());
                return;
            }
            threadPoolTaskExecutor.submit(() -> supplier.get());
            long end = System.currentTimeMillis();
            long cost = end - start;
            log.info("ScheduledHelper 定时任务 finish ok cost:{} ms {}, ", cost, scheduledType.toString());
            ScheduledLogDO updateEntity = logDO.successByLock(cost);
            scheduledLogRepositoryService.updateById(updateEntity);
        } catch (Exception e) {
            long end = System.currentTimeMillis();
            long cost = end - start;
            log.info("ScheduledHelper 定时任务 finish fail cost:{}ms {},e:{}", cost, scheduledType.toString(), Throwables.getStackTraceAsString(e));
            ScheduledLogDO updateEntity = logDO.failByException(cost);
            scheduledLogRepositoryService.updateById(updateEntity);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

}

package com.avatar.hospital.chaperone.builder.hospital;

import com.avatar.hospital.chaperone.database.hospital.dataobject.HospitalDO;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.hospital.HospitalPagingResponse;
import com.avatar.hospital.chaperone.response.hospital.HospitalResourceDataResponse;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class HospitalBuilder {

    public static PageResponse<HospitalPagingResponse> buildPagingDO(PageResponse<HospitalDO> pageResponse) {
        if (pageResponse == null) {
            return null;
        }
        PageResponse<HospitalPagingResponse> response = new PageResponse<>();
        response.setTotal(pageResponse.getTotal());
        response.setSize(pageResponse.getSize());
        response.setCurrent(pageResponse.getCurrent());
        response.setRecords(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(pageResponse.getRecords())) {
            response.setRecords(buildPagingDO(pageResponse.getRecords()));
        }
        return response;
    }

    public static List<HospitalPagingResponse> buildPagingDO(List<HospitalDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        List<HospitalPagingResponse> list = new ArrayList<>();

        for (HospitalDO hospitalDO : records) {
            HospitalPagingResponse hospitalPagingResponse = new HospitalPagingResponse();
            hospitalPagingResponse.setId(hospitalDO.getId());
            hospitalPagingResponse.setName(hospitalDO.getName());
            hospitalPagingResponse.setAddress(hospitalDO.getAddress());
            hospitalPagingResponse.setRemark(hospitalDO.getRemark());
            list.add(hospitalPagingResponse);
        }
        return list;
    }

    public static List<HospitalResourceDataResponse> buildResourceData(List<HospitalDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        ArrayList<HospitalResourceDataResponse> list = new ArrayList<>();

        for (HospitalDO hospitalDO : records) {
            HospitalResourceDataResponse resourceDataResponse = new HospitalResourceDataResponse();
            resourceDataResponse.setId(hospitalDO.getId());
            resourceDataResponse.setName(hospitalDO.getName());
            list.add(resourceDataResponse);
        }
        return list;
    }

}

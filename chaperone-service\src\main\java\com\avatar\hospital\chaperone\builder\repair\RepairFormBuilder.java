package com.avatar.hospital.chaperone.builder.repair;

import com.avatar.hospital.chaperone.context.UserContext;
import com.avatar.hospital.chaperone.database.device.dataobject.ProjectDeviceDO;
import com.avatar.hospital.chaperone.database.part.dataobject.model.PartApplyModel;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormFeedbackDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormTaskDO;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormSourceType;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormStatus;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormSystemType;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormUrgencyDegreeType;
import com.avatar.hospital.chaperone.request.repair.RepairFormAssignExecutorRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormCreateRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormExportRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormModifyRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormPageRequest;
import com.avatar.hospital.chaperone.response.repair.*;
import com.avatar.hospital.chaperone.response.repair.dto.RepairFormExportDTO;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:58
 **/
public class RepairFormBuilder {

    /**
     * 默认人名
     */
    private static final String DEFAULT_NAME = "";

    /**
     * 默认设备名称
     */
    private static final String DEFAULT_DEVICE_NAME = "";
    /**
     * 默认派分人员
     */
    private static final Long DEFAULT_ASSIGNER_ACCOUNTID = 0L;
    /**
     * 默认业务ID
     */
    private static final Long DEFAULT_BIZ_ID = 0L;

    public static List<RepairFormResponse> toRepairFormResponseList(List<RepairFormDO> records, List<RepairFormTaskDO> taskDOList, Map<Long, String> accountIdNameRef, Map<Long, RepairFormFeedbackResponse> lastFeedbackRef) {
        Map<Long, List<RepairFormTaskDO>> taskMap = taskDOList.stream().collect(Collectors.groupingBy(RepairFormTaskDO::getRepairFormId));
        List<RepairFormResponse> list = new ArrayList(records.size());
        for (RepairFormDO record : records) {
            List<RepairFormTaskDO> taskList = taskMap.get(record.getId());
            List<AccountModel> accountModelList = null;
            if (!CollectionUtils.isEmpty(taskList)) {
                accountModelList = taskList.stream().map(item -> new AccountModel(item.getExecutorAccountId(), accountIdNameRef.getOrDefault(item.getExecutorAccountId(), DEFAULT_NAME))).collect(Collectors.toList());
            }

            RepairFormFeedbackResponse response = lastFeedbackRef.get(record.getId());

            RepairFormResponse result = new RepairFormResponse();
            result.setId(record.getId());
            result.setCode(record.getCode());
            result.setSystemType(record.getSystemType());
            result.setStatus(record.getStatus());
            result.setCreateBy(record.getCreateBy());
            result.setCreateByName(accountIdNameRef.getOrDefault(record.getCreateBy(), DEFAULT_NAME));
            result.setCreateAt(record.getCreatedAt());
            result.setRemark(record.getRemark());
            result.setDeviceId(record.getDeviceId());
            result.setDeviceName(record.getDeviceName());
            result.setAttachments(record.getAttachments());
            result.setSourceType(record.getSourceType());
            result.setBizId(record.getBizId());
            result.setBizType(record.getBizType());
            result.setCompletedTime(record.getCompletedTime());
            result.setAssignerAccountId(record.getAssignerAccountId());
            result.setAssignerAccountName(accountIdNameRef.getOrDefault(record.getAssignerAccountId(), DEFAULT_NAME));
            result.setExecutorAccountList(accountModelList);
            result.setLocation(record.getLocation());
            if (Objects.nonNull(response)) {
                String remark = response.getRemark();
                result.setDesc(remark);
            }
            result.setUrgencyDegreeType(record.getUrgencyDegreeType());
            list.add(result);
        }
        return list;
    }

    public static RepairFormIdResponse buildRepairFormIdResponse(Long id) {
        return RepairFormIdResponse.builder().id(id).build();
    }

    public static RepairFormDO createByPCCreate(RepairFormCreateRequest request, String code, Long id, ProjectDeviceDO deviceDO) {
        Long operator = request.getOperator();
        String operatorName = request.getOperatorUser().getName();

        RepairFormDO repairFormDO = new RepairFormDO();
        repairFormDO.setId(id);
        repairFormDO.setCode(code);
        repairFormDO.setStatus(RepairFormStatus.INIT.getStatus());
        repairFormDO.setRemark(request.getRemark());
        repairFormDO.setSystemType(request.getSystemType());
        repairFormDO.setDeviceId(request.getDeviceId());
        repairFormDO.setDeviceName(deviceDO.getName());
        repairFormDO.setAttachments(request.getAttachments());
        repairFormDO.setSourceType(RepairFormSourceType.PC.getStatus());
        repairFormDO.setBizId(request.getBizId());
        repairFormDO.setBizType(request.getBizType());
        repairFormDO.setAssignerAccountId(DEFAULT_ASSIGNER_ACCOUNTID);
        repairFormDO.setCreateBy(operator);
        repairFormDO.setCreateByName(operatorName);
        repairFormDO.setUpdateBy(operator);
        repairFormDO.setDeleted(DelUtils.NO_DELETED);
        repairFormDO.setLocation(request.getLocation());
        return repairFormDO;
    }

    public static RepairFormDO createByH5Create(RepairFormCreateRequest request, String code, Long id, ProjectDeviceDO deviceDO) {
        Long operator = request.getOperator();
        String operatorName = request.getOperatorUser().getName();

        RepairFormDO repairFormDO = new RepairFormDO();
        repairFormDO.setId(id);
        repairFormDO.setHid(UserContext.getHospitalId());
        repairFormDO.setCode(code);
        repairFormDO.setStatus(RepairFormStatus.INIT.getStatus());
        repairFormDO.setRemark(request.getRemark());
        repairFormDO.setSystemType(request.getSystemType());
        repairFormDO.setDeviceId(request.getDeviceId());
        repairFormDO.setDeviceName(deviceDO.getName());
        repairFormDO.setAttachments(request.getAttachments());
        repairFormDO.setSourceType(RepairFormSourceType.H5.getStatus());
        repairFormDO.setBizId(request.getBizId());
        repairFormDO.setBizType(request.getBizType());
        repairFormDO.setAssignerAccountId(DEFAULT_ASSIGNER_ACCOUNTID);
        repairFormDO.setCreateBy(operator);
        repairFormDO.setCreateByName(operatorName);
        repairFormDO.setUpdateBy(operator);
        repairFormDO.setDeleted(DelUtils.NO_DELETED);
        repairFormDO.setLocation(request.getLocation());
        return repairFormDO;
    }

    public static RepairFormDO modifyByModify(RepairFormDO repairFormDO, RepairFormModifyRequest request, ProjectDeviceDO deviceDO) {
        RepairFormDO updateEntity = new RepairFormDO();
        updateEntity.setId(request.getId());
        updateEntity.setRemark(request.getRemark());
        updateEntity.setSystemType(request.getSystemType());
        updateEntity.setDeviceId(request.getDeviceId());
        updateEntity.setDeviceName(deviceDO.getName());
        updateEntity.setAttachments(request.getAttachments());
        updateEntity.setSourceType(request.getSourceType());
        updateEntity.setBizId(request.getBizId());
        updateEntity.setBizType(request.getBizType());
        updateEntity.setUpdateBy(request.getOperator());
        updateEntity.setUpdatedAt(LocalDateTime.now());
        updateEntity.setLocation(request.getLocation());
        return updateEntity;
    }

    public static List<RepairFormTaskDO> createAssignExecutor(RepairFormDO repairFormDO, List<RepairFormTaskDO> dbRepairFormTaskDOList, RepairFormAssignExecutorRequest request) {
        Long repairFormId = repairFormDO.getId();
        Long operator = request.getOperator();
        Long delVersion = DelUtils.delVersion();
        dbRepairFormTaskDOList.forEach(task -> task.setDeleted(delVersion));
        Map<Long, RepairFormTaskDO> dbMap = dbRepairFormTaskDOList.stream().collect(Collectors.toMap(RepairFormTaskDO::getExecutorAccountId, Function.identity()));

        Set<Long> executorAccountIdSet = new HashSet<>();
        List<RepairFormTaskDO> collect = request.getExecutorAccountIdList().stream().map(executorAccountId -> {
            executorAccountIdSet.add(executorAccountId);
            RepairFormTaskDO taskDO = dbMap.get(executorAccountId);
            if (Objects.isNull(taskDO)) {
                return createRepairFormTaskDO(repairFormId, executorAccountId, operator);
            } else {
                taskDO.setDeleted(DelUtils.NO_DELETED);
                return taskDO;
            }
        }).collect(Collectors.toList());

        dbRepairFormTaskDOList.stream().filter(executorAccountId -> !executorAccountIdSet.contains(executorAccountId)).forEach(item -> collect.add(item));
        return collect;
    }

    public static RepairFormTaskDO createRepairFormTaskDO(Long repairFormId, Long executorAccountId, Long assignerAccountId) {
        RepairFormTaskDO taskDO = new RepairFormTaskDO();
        taskDO.setRepairFormId(repairFormId);
        taskDO.setExecutorAccountId(executorAccountId);
        taskDO.setAssignerAccountId(assignerAccountId);
        taskDO.setCreateBy(assignerAccountId);
        taskDO.setUpdateBy(assignerAccountId);
        taskDO.setDeleted(DelUtils.NO_DELETED);
        taskDO.setHid(UserContext.getHospitalId());
        return taskDO;
    }

    public static RepairFormDO createModifyAssignerAccountId(RepairFormDO repairFormDO, RepairFormAssignExecutorRequest request) {
        RepairFormDO updateEntity = new RepairFormDO();
        updateEntity.setId(repairFormDO.getId());
        updateEntity.setAssignerAccountId(request.getOperator());
        updateEntity.setStatus(RepairFormStatus.HAS_EXECUTOR.getStatus());
        updateEntity.setUpdatedAt(LocalDateTime.now());
        updateEntity.setUrgencyDegreeType(request.getUrgencyDegreeType());
        return updateEntity;
    }

    public static RepairFormDetailResponse creatRepairFormDetailResponse(RepairFormDO repairFormDO) {
        return RepairFormDetailResponse.builder().id(repairFormDO.getId()).code(repairFormDO.getCode()).systemType(repairFormDO.getSystemType()).status(repairFormDO.getStatus()).createBy(repairFormDO.getCreateBy()).createByName(repairFormDO.getCreateByName()).createAt(repairFormDO.getCreatedAt()).remark(repairFormDO.getRemark()).deviceId(repairFormDO.getDeviceId()).deviceName(repairFormDO.getDeviceName()).attachments(repairFormDO.getAttachments()).sourceType(repairFormDO.getSourceType()).bizId(repairFormDO.getBizId()).bizType(repairFormDO.getBizType()).completedTime(repairFormDO.getCompletedTime()).urgencyDegreeType(repairFormDO.getUrgencyDegreeType()).location(repairFormDO.getLocation()).build();
    }

    public static List<RepairFormCResponse> toRepairFormCResponseList(List<RepairFormDO> records, List<RepairFormTaskDO> repairFormTaskDOList, Map<Long, String> accountIdNameRef, Map<Long, String> deviceIdNameRef) {
        Map<Long, List<RepairFormTaskDO>> taskMap = repairFormTaskDOList.stream().collect(Collectors.groupingBy(RepairFormTaskDO::getRepairFormId));

        List<RepairFormCResponse> list = new ArrayList(records.size());
        for (RepairFormDO record : records) {
            List<RepairFormTaskDO> taskDOList = taskMap.get(record.getId());
            List<String> executorAcountNameList = null;
            if (!CollectionUtils.isEmpty(taskDOList)) {
                executorAcountNameList = taskDOList.stream().map(item -> accountIdNameRef.get(item.getExecutorAccountId())).filter(name -> Objects.nonNull(name)).collect(Collectors.toList());
            }

            RepairFormCResponse result = new RepairFormCResponse();
            result.setId(record.getId());
            result.setCode(record.getCode());
            result.setSystemType(record.getSystemType());
            result.setStatus(record.getStatus());
            result.setCreateBy(record.getCreateBy());
            result.setCreateByName(accountIdNameRef.getOrDefault(record.getCreateBy(), DEFAULT_NAME));
            result.setCreateAt(record.getCreatedAt());
            result.setRemark(record.getRemark());
            result.setDeviceId(record.getDeviceId());
            result.setDeviceName(deviceIdNameRef.getOrDefault(record.getDeviceId(), DEFAULT_DEVICE_NAME));
            result.setExecutorAccountName(executorAcountNameList);
            result.setUrgencyDegreeType(record.getUrgencyDegreeType());
            result.setLocation(record.getLocation());
            list.add(result);
        }
        return list;
    }

    public static List<RepairFormFeedbackResponse> createRepairFormFeedbackResponseList(List<RepairFormFeedbackDO> records, List<RepairFormDO> repairFormList, Map<Long, List<PartApplyModel>> feedbackPartRef) {
        Map<Long, RepairFormDO> repairFormDOMap = repairFormList.stream().collect(Collectors.toMap(RepairFormDO::getId, Function.identity()));
        List<RepairFormFeedbackResponse> list = new ArrayList<>();
        for (RepairFormFeedbackDO record : records) {
            RepairFormDO repairFormDO = repairFormDOMap.get(record.getRepairFormId());

            RepairFormFeedbackResponse response = new RepairFormFeedbackResponse();
            if (Objects.nonNull(repairFormDO)) {
                response.setCode(repairFormDO.getCode());
                response.setSystemType(repairFormDO.getSystemType());
            }
            response.setCreateBy(record.getCreateBy());
            response.setCreateByName(record.getCreateByName());
            response.setCreateAt(record.getCreatedAt());
            response.setFeedbackType(record.getFeedbackType());
            response.setRemark(record.getRemark());
            response.setAttachments(record.getAttachments());
            response.setPart(feedbackPartRef.get(record.getId()));
            list.add(response);
        }
        return list;
    }

    public static RepairFormPageRequest toRepairFormPageRequest(RepairFormExportRequest param) {
        RepairFormPageRequest repairFormPageRequest = new RepairFormPageRequest();
        repairFormPageRequest.setCode(param.getCode());
        repairFormPageRequest.setSystemType(param.getSystemType());
        repairFormPageRequest.setStatus(param.getStatus());
        if (param.getOperatorUser() != null) {
            Operator operatorUser = new Operator();
            operatorUser.setId(param.getOperatorUser().getId());
            operatorUser.setMobile(param.getOperatorUser().getMobile());
            operatorUser.setName(param.getOperatorUser().getName());
            operatorUser.setSource(param.getOperatorUser().getSource());
            repairFormPageRequest.setOperatorUser(operatorUser);
        }
        repairFormPageRequest.setBizType(param.getBizType());
        repairFormPageRequest.setBizIds(param.getBizIds());
        repairFormPageRequest.setPageIndex(param.getPageIndex());
        repairFormPageRequest.setPageSize(param.getPageSize());
        return repairFormPageRequest;
    }

    public static List<RepairFormExportDTO> toRepairFormExportDTOList(List<RepairFormResponse> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        List<RepairFormExportDTO> repairFormExportList = Lists.newArrayListWithCapacity(records.size());
        records.forEach(repairFormResponse -> repairFormExportList.add(toRepairFormExport(repairFormResponse)));
        return repairFormExportList;
    }

    public static RepairFormExportDTO toRepairFormExport(RepairFormResponse repairFormResponse) {
        RepairFormExportDTO repairFormExportDTO = new RepairFormExportDTO();
        repairFormExportDTO.setCode(repairFormResponse.getCode());

        if (repairFormResponse.getSystemType() != null) {
            RepairFormSystemType repairFormSystemType = RepairFormSystemType.of(repairFormResponse.getSystemType());
            if (repairFormSystemType != null) {
                repairFormExportDTO.setSystemType(repairFormSystemType.getDescribe());
            }
        }

        if (RepairFormStatus.INIT.getStatus().equals(repairFormResponse.getStatus())) {
            repairFormExportDTO.setStatus("未指派");
        } else if (RepairFormStatus.HAS_EXECUTOR.getStatus().equals(repairFormResponse.getStatus())) {
            repairFormExportDTO.setStatus("已指派");
        } else if (RepairFormStatus.NOT_FINISH.getStatus().equals(repairFormResponse.getStatus())) {
            repairFormExportDTO.setStatus("未完成");
        } else if (RepairFormStatus.AUTH.getStatus().equals(repairFormResponse.getStatus())) {
            repairFormExportDTO.setStatus("待审核");
        } else if (RepairFormStatus.FINISH.getStatus().equals(repairFormResponse.getStatus())) {
            repairFormExportDTO.setStatus("已完成");
        } else {
            repairFormExportDTO.setStatus("未知");
        }

        repairFormExportDTO.setCreateByName(repairFormResponse.getCreateByName());
        if (repairFormResponse.getCreateAt() != null) {
            repairFormExportDTO.setCreateAt(repairFormResponse.getCreateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (repairFormResponse.getCompletedTime() != null) {
            repairFormExportDTO.setCompletedTime(repairFormResponse.getCompletedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        repairFormExportDTO.setAssignerAccountName(repairFormResponse.getAssignerAccountName());
        if (repairFormResponse.getUrgencyDegreeType() != null) {
            RepairFormUrgencyDegreeType repairFormUrgencyDegreeType = RepairFormUrgencyDegreeType.of(repairFormResponse.getUrgencyDegreeType());
            if (repairFormUrgencyDegreeType != null) {
                repairFormExportDTO.setUrgencyDegreeType(repairFormUrgencyDegreeType.getDescribe());
            }
        }
        if (!CollectionUtils.isEmpty(repairFormResponse.getExecutorAccountList())) {
            Set<String> executorAccountNames = repairFormResponse.getExecutorAccountList().stream().map(AccountModel::getName).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(executorAccountNames)) {
                repairFormExportDTO.setExecutorAccountName(Joiner.on(",").join(executorAccountNames));
            }
        }
        repairFormExportDTO.setLocation(repairFormResponse.getLocation());
        repairFormExportDTO.setRemark(repairFormResponse.getRemark());
        repairFormExportDTO.setDesc(repairFormResponse.getDesc());
        return repairFormExportDTO;
    }

    public static RepairFormFeedbackResponse buildRepairFormFeedbackResponseForRemark(RepairFormFeedbackDO record) {
        return RepairFormFeedbackResponse.builder().remark(record.getRemark()).build();
    }
}

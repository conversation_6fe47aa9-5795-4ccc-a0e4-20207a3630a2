package com.avatar.hospital.chaperone.service.device.impl;

import com.avatar.hospital.chaperone.builder.device.DeviceBuilder;
import com.avatar.hospital.chaperone.builder.emergency.EmergencyLogBuilder;
import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.baccount.repository.OrganizationRepositoryService;
import com.avatar.hospital.chaperone.database.device.dataobject.ProjectDeviceDO;
import com.avatar.hospital.chaperone.database.device.repository.ProjectDeviceRepositoryService;
import com.avatar.hospital.chaperone.database.emergencylog.dataobject.EmergencyHandlingLogDO;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.device.DeviceAddRequest;
import com.avatar.hospital.chaperone.request.device.DeviceCPagingRequest;
import com.avatar.hospital.chaperone.request.device.DevicePagingRequest;
import com.avatar.hospital.chaperone.request.device.DeviceUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.device.DeviceAddResponse;
import com.avatar.hospital.chaperone.response.device.DevicePagingResponse;
import com.avatar.hospital.chaperone.response.device.DeviceUpdateResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogPagingResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogUpdateResponse;
import com.avatar.hospital.chaperone.service.device.DeviceService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/31 10:16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DeviceServiceImpl implements DeviceService {
    @Autowired
    private ProjectDeviceRepositoryService deviceRepositoryService;
    @Autowired
    private OrganizationRepositoryService organizationRepositoryService;

    /**
     * 查询设备映射
     *
     * @param deviceIds
     * @return
     */
    @Override
    public Map<Long, String> findDeviceMap(Set<Long> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return Maps.newHashMap();
        }
        Map<Long, String> map = deviceRepositoryService.findMapByIdList(new ArrayList<>(deviceIds));
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceAddResponse add(DeviceAddRequest request) {
        Long id = deviceRepositoryService.add(DeviceBuilder.buildEmergencyLogDO(request));
        return DeviceAddResponse.builder().id(id).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceUpdateResponse update(DeviceUpdateRequest request) {
        ProjectDeviceDO date = deviceRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(date, ErrorCode.NURSING_NOT_EXIST);
        Boolean result = deviceRepositoryService.incrementUpdate(DeviceBuilder.buildEmergencyLogDO(request));
        return DeviceUpdateResponse.builder().success(result).build();
    }

    @Override
    public PageResponse<DevicePagingResponse> paging(DevicePagingRequest request) {
        PageResponse<ProjectDeviceDO> paging = deviceRepositoryService.paging(request);
        PageResponse<DevicePagingResponse> pageResponse = DeviceBuilder.buildEmergencyLogDO(paging);
        if (Objects.nonNull(pageResponse) && !org.springframework.util.CollectionUtils.isEmpty(pageResponse.getRecords())) {
            List<DevicePagingResponse> records = pageResponse.getRecords();
            Set<Long> orgIdList = new HashSet<>();
            Set<Long> collect2 = records.stream().map(DevicePagingResponse::getOrgDepartmentId).collect(Collectors.toSet());
            orgIdList.addAll(collect2);
            List<OrganizationDO> organizationDOList = organizationRepositoryService.findByIds(orgIdList);
            if (!CollectionUtils.isEmpty(organizationDOList)) {
                Map<Long, OrganizationDO> longOrganizationDOMap = organizationDOList.stream().collect(Collectors.toMap(OrganizationDO::getId, Function.identity()));
                records.forEach(i -> {
                    if (Objects.nonNull(longOrganizationDOMap.get(i.getOrgDepartmentId()))) {
                        i.setOrgDepartmentName(longOrganizationDOMap.get(i.getOrgDepartmentId()).getName());
                    }
                });
            }
        }
        return pageResponse;
    }

    @Override
    public PageResponse<DevicePagingResponse> pagingForC(DeviceCPagingRequest request) {
        Page<ProjectDeviceDO> page = request.ofPage();
        LambdaQueryWrapper<ProjectDeviceDO> queryWrapper = queryWrapper();
        queryWrapper.eq(Objects.nonNull(request.getSystemType()), ProjectDeviceDO::getSystemType, request.getSystemType());
        queryWrapper.eq(Objects.nonNull(request.getId()), ProjectDeviceDO::getId, request.getId());
        queryWrapper.likeLeft(Objects.nonNull(request.getName()), ProjectDeviceDO::getName, request.getName());
        queryWrapper.orderByDesc(ProjectDeviceDO::getCreateBy);
        page = deviceRepositoryService.page(page, queryWrapper);
        return PageResponse.build(page, DeviceBuilder::buildEmergencyLogDO);
    }

    private LambdaQueryWrapper<ProjectDeviceDO> queryWrapper() {
        LambdaQueryWrapper<ProjectDeviceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectDeviceDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }
}

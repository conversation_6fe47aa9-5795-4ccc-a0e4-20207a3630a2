package com.avatar.hospital.chaperone.database.plan.repository;

import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanTaskDO;
import com.avatar.hospital.chaperone.database.plan.repository.base.PlanTaskRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.request.plan.TaskExecuteRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 巡检计划任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PatrolPlanTaskRepositoryService extends IService<PatrolPlanTaskDO>, PlanTaskRepositoryService {

    LambdaQueryWrapper<PatrolPlanTaskDO> queryWrapper();

    <PERSON><PERSON><PERSON> execute(TaskExecuteRequest request);

    Boolean expired(List<Long> taskIds);

    boolean update2delete(PlanRequest request);

    List<PatrolPlanTaskDO> listWithoutTenant(LambdaQueryWrapper<PatrolPlanTaskDO> queryWrapper);

    /**
     * 按年统计
     *
     * @param year
     * @return
     */
    Map<Integer, Integer> getStatisticsForYear(Integer year);

    /**
     * 按月统计
     *
     * @param month
     * @return
     */
    Map<Integer, Integer> getStatisticsForMonth(Integer month);
}

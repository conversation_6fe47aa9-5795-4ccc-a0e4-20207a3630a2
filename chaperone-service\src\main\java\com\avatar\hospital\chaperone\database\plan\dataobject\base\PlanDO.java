package com.avatar.hospital.chaperone.database.plan.dataobject.base;

import com.avatar.hospital.chaperone.database.part.dataobject.base.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 巡检计划
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
public class PlanDO extends TenantBaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 巡检周期类型（1-天，2-月）
     */
    private Integer circleType;

    /**
     * 巡检周期
     */
    private Integer circle;
    /**
     * 状态（1-生效，2-作废）
     *
     * @see com.avatar.hospital.chaperone.database.plan.enums.PlanStatusType
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

}

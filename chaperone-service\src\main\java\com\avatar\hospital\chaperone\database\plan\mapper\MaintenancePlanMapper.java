package com.avatar.hospital.chaperone.database.plan.mapper;

import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanDO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 维保计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface MaintenancePlanMapper extends BaseMapper<MaintenancePlanDO> {

    // 忽略多租户插件，查询所有租户的数据
    @InterceptorIgnore(tenantLine = "true")
    @Select("SELECT * FROM t_project_maintenance_plan ${ew.customSqlSegment}")
    List<MaintenancePlanDO> selectAllWithoutTenant(@Param("ew") LambdaQueryWrapper<MaintenancePlanDO> queryWrapper);


}

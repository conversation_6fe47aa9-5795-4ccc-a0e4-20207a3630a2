package com.avatar.hospital.chaperone.database.plan.dataobject.base;

import com.avatar.hospital.chaperone.database.part.dataobject.base.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 巡检计划关联设备
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
public class PlanRefDeviceDO extends TenantBaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 巡检计划ID
     */
    private Long planId;

    /**
     * 设备ID
     */
    private Long deviceId;


}

package com.avatar.hospital.chaperone.database.baccount.mapper;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * B端用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Mapper
public interface WebAccountMapper extends BaseMapper<AccountDO> {

    /**
     * 根据手机号和院区ID查询用户（忽略租户隔离）
     * 注意：@InterceptorIgnore注解必须在Mapper方法上才能生效
     */
    @InterceptorIgnore(tenantLine = "true")
    @Select("SELECT * FROM t_b_account WHERE phone_number = #{phoneNumber} AND hid = #{hid} AND deleted = 0")
    AccountDO selectByPhoneNumberAndHidIgnoreTenant(@Param("phoneNumber") String phoneNumber, @Param("hid") Long hid);

}

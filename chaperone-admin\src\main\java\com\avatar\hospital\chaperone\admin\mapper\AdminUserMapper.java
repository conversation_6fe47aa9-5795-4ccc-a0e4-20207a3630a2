package com.avatar.hospital.chaperone.admin.mapper;

import com.avatar.hospital.chaperone.admin.entity.AdminUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 超级管理员用户Mapper
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface AdminUserMapper extends BaseMapper<AdminUser> {

    /**
     * 根据用户名查询管理员用户
     * 
     * @param username 用户名
     * @return 管理员用户
     */
    @Select("SELECT * FROM t_admin_user WHERE username = #{username} AND deleted = 0")
    AdminUser selectByUsername(@Param("username") String username);

    /**
     * 根据手机号查询管理员用户
     * 
     * @param phone 手机号
     * @return 管理员用户
     */
    @Select("SELECT * FROM t_admin_user WHERE phone = #{phone} AND deleted = 0")
    AdminUser selectByPhone(@Param("phone") String phone);

    /**
     * 更新最后登录信息
     * 
     * @param id 用户ID
     * @param loginTime 登录时间
     * @param loginIp 登录IP
     * @return 更新结果
     */
    @Update("UPDATE t_admin_user SET last_login_time = #{loginTime}, last_login_ip = #{loginIp} WHERE id = #{id}")
    int updateLastLoginInfo(@Param("id") Long id, 
                           @Param("loginTime") java.time.LocalDateTime loginTime, 
                           @Param("loginIp") String loginIp);

}

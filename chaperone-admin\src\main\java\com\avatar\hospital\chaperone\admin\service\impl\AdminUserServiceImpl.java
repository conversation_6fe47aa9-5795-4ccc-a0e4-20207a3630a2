package com.avatar.hospital.chaperone.admin.service.impl;

import com.avatar.hospital.chaperone.admin.entity.AdminUser;
import com.avatar.hospital.chaperone.admin.mapper.AdminUserMapper;
import com.avatar.hospital.chaperone.admin.service.AdminUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 超级管理员用户服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class AdminUserServiceImpl extends ServiceImpl<AdminUserMapper, AdminUser> implements AdminUserService {

    private static final String PASSWORD_SALT = "ADMIN_SALT_2024";

    @Override
    public AdminUser getByUsername(String username) {
        return baseMapper.selectByUsername(username);
    }

    @Override
    public AdminUser getByPhone(String phone) {
        return baseMapper.selectByPhone(phone);
    }

    @Override
    public boolean verifyPassword(String rawPassword, String encodedPassword) {
        String encoded = encodePassword(rawPassword);
        return Objects.equals(encoded, encodedPassword);
    }

    @Override
    public String encodePassword(String rawPassword) {
        return DigestUtils.md5DigestAsHex((rawPassword + PASSWORD_SALT).getBytes());
    }

    @Override
    public boolean updateLastLoginInfo(Long userId, String loginIp) {
        int result = baseMapper.updateLastLoginInfo(userId, LocalDateTime.now(), loginIp);
        return result > 0;
    }

    @Override
    public boolean isUsernameExists(String username, Long excludeId) {
        LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AdminUser::getUsername, username);
        if (excludeId != null) {
            wrapper.ne(AdminUser::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    @Override
    public boolean isPhoneExists(String phone, Long excludeId) {
        LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AdminUser::getPhone, phone);
        if (excludeId != null) {
            wrapper.ne(AdminUser::getId, excludeId);
        }
        return count(wrapper) > 0;
    }
}

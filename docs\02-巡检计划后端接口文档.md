# 02-巡检计划后端接口文档

## 概述

本文档详细介绍了医院陪护系统中巡检计划相关的后端接口，包括控制器层、服务层、数据访问层的实现，以及接口的调用方式和参数说明。

## 控制器层 (Controller)

### 1. PatrolPlanController (巡检计划控制器)

**文件位置**: `chaperone-web/src/main/java/com/avatar/hospital/chaperone/web/controller/plan/PatrolPlanController.java`

**基础路径**: `/api/v1/web/project/plan/patrol`

#### 1.1 创建巡检计划

**接口信息**:
- **URL**: `POST /api/v1/web/project/plan/patrol/create`
- **功能**: 创建新的巡检计划
- **幂等性**: 支持（基于 circleType + orgId + planType）

**请求参数**:
```java
@PostMapping("create")
public SingleResponse<Long> create(@RequestBody PlanRequest request)
```

**请求体示例**:
```json
{
    "name": "设备日常巡检计划",
    "circleType": 1,
    "circle": 1,
    "remark": "每日设备巡检",
    "orgId": 1001,
    "deviceIds": [1, 2, 3],
    "executorIds": [101, 102],
    "orgIds": [1001, 1002]
}
```

**响应示例**:
```json
{
    "success": true,
    "data": 12345,
    "message": "创建成功"
}
```

#### 1.2 更新巡检计划

**接口信息**:
- **URL**: `POST /api/v1/web/project/plan/patrol/update`
- **功能**: 更新现有巡检计划
- **幂等性**: 支持（基于计划ID）

**请求参数**:
```java
@PostMapping("update")
public SingleResponse<Boolean> update(@RequestBody PlanRequest request)
```

**请求体示例**:
```json
{
    "id": 12345,
    "name": "设备日常巡检计划（更新）",
    "circleType": 1,
    "circle": 2,
    "remark": "每2天设备巡检",
    "deviceIds": [1, 2, 3, 4],
    "executorIds": [101, 102, 103]
}
```

#### 1.3 分页查询巡检计划

**接口信息**:
- **URL**: `GET /api/v1/web/project/plan/patrol/paging`
- **功能**: 分页查询巡检计划列表

**请求参数**:
```java
@GetMapping("paging")
public SingleResponse<PageResponse<PlanVO>> paging(QueryRequest request)
```

**查询参数示例**:
```
GET /api/v1/web/project/plan/patrol/paging?pageNum=1&pageSize=10&name=设备&status=1
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "list": [
            {
                "id": "12345",
                "code": "XJ20231101001",
                "name": "设备日常巡检计划",
                "circleType": 1,
                "circle": 1,
                "status": 1,
                "remark": "每日设备巡检",
                "orgId": "1001",
                "createdAt": "2023-11-01T10:00:00"
            }
        ],
        "total": 1,
        "pageNum": 1,
        "pageSize": 10
    }
}
```

#### 1.4 查询巡检计划详情

**接口信息**:
- **URL**: `GET /api/v1/web/project/plan/patrol/detail/{id}`
- **功能**: 根据ID查询巡检计划详细信息

**请求参数**:
```java
@GetMapping("detail/{id}")
public SingleResponse<PlanVO> getById(@PathVariable("id") Long id)
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": "12345",
        "code": "XJ20231101001",
        "name": "设备日常巡检计划",
        "circleType": 1,
        "circle": 1,
        "status": 1,
        "remark": "每日设备巡检",
        "orgId": "1001",
        "deviceList": [
            {"id": "1", "name": "设备A"},
            {"id": "2", "name": "设备B"}
        ],
        "executorList": [
            {"id": "101", "name": "张三"},
            {"id": "102", "name": "李四"}
        ],
        "createdAt": "2023-11-01T10:00:00"
    }
}
```

#### 1.5 作废巡检计划

**接口信息**:
- **URL**: `DELETE /api/v1/web/project/plan/patrol/abandon/{id}`
- **功能**: 作废指定的巡检计划

**请求参数**:
```java
@DeleteMapping("abandon/{id}")
public SingleResponse<Boolean> abandon(@PathVariable("id") Long id)
```

**响应示例**:
```json
{
    "success": true,
    "data": true,
    "message": "作废成功"
}
```

### 2. PatrolPlanTaskController (巡检计划任务控制器)

**文件位置**: `chaperone-web/src/main/java/com/avatar/hospital/chaperone/web/controller/plan/PatrolPlanTaskController.java`

**基础路径**: `/api/v1/web/project/plan/patrol/task`

#### 2.1 分页查询巡检任务

**接口信息**:
- **URL**: `GET /api/v1/web/project/plan/patrol/task/paging`
- **功能**: 分页查询巡检任务列表

**请求参数**:
```java
@GetMapping("paging")
public SingleResponse<PageResponse<PlanTaskVO>> paging(QueryRequest request)
```

**查询参数示例**:
```
GET /api/v1/web/project/plan/patrol/task/paging?pageNum=1&pageSize=10&status=0&planId=12345
```

#### 2.2 查询我的巡检任务

**接口信息**:
- **URL**: `GET /api/v1/web/project/plan/patrol/task/mine`
- **功能**: 查询当前用户的巡检任务

**请求参数**:
```java
@GetMapping("mine")
public SingleResponse<PageResponse<PlanTaskVO>> mine(QueryRequest request)
```

**特殊逻辑**:
- 自动获取当前用户ID
- 自动获取用户所属部门的组织列表
- 只返回与用户相关的任务

#### 2.3 执行巡检任务

**接口信息**:
- **URL**: `PUT /api/v1/web/project/plan/patrol/task/execute`
- **功能**: 执行巡检任务，记录巡检结果

**请求参数**:
```java
@PutMapping("execute")
public SingleResponse<Boolean> execute(@RequestBody TaskExecuteRequest request)
```

**请求体示例**:
```json
{
    "id": 54321,
    "remark": "设备运行正常，无异常",
    "isRepair": false
}
```

**如需报修的请求体示例**:
```json
{
    "id": 54321,
    "remark": "设备有异响，需要维修",
    "isRepair": true,
    "repairFormCreateRequest": {
        "deviceId": 1,
        "faultDescription": "设备异响",
        "urgencyLevel": 2
    }
}
```

## 服务层 (Service)

### 3. PlanService (计划服务接口)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/service/plan/PlanService.java`

#### 3.1 接口定义

```java
public interface PlanService {
    Long create(PlanRequest request);                           // 创建计划
    Boolean update(PlanRequest request);                        // 更新计划
    PageResponse<PlanVO> paging(QueryRequest request);          // 分页查询
    PlanVO detail(PlanType planType, Long id);                 // 查询详情
    void execute(PlanType planType);                            // 执行计划（生成任务）
    Boolean abandon(PlanRequest request);                       // 作废计划
}
```

### 4. PlanServiceImpl (计划服务实现)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/service/plan/impl/PlanServiceImpl.java`

#### 4.1 创建计划逻辑

```java
@Transactional(rollbackFor = Exception.class)
@Override
public Long create(PlanRequest request) {
    // 1. 构建计划实体
    PlanDO planDO = PlanBuilder.build(request);
    
    // 2. 保存计划主表
    planRepositoryService.save(request.getPlanType(), planDO);
    
    // 3. 保存关联设备
    if (CollectionUtils.isNotEmpty(request.getDeviceIds())) {
        refDeviceRepositoryService.saveBatch(request.getPlanType(), 
            PlanBuilder.buildRefDevice(request));
    }
    
    // 4. 保存关联执行人员
    if (CollectionUtils.isNotEmpty(request.getExecutorIds())) {
        refExecutorRepositoryService.saveBatch(request.getPlanType(), 
            PlanBuilder.buildRefExecutor(request));
    }
    
    // 5. 保存关联部门
    if (CollectionUtils.isNotEmpty(request.getOrgIds())) {
        refOrgRepositoryService.saveBatch(request.getPlanType(), 
            PlanBuilder.buildRefOrg(request));
    }
    
    return planDO.getId();
}
```

#### 4.2 执行计划逻辑（任务生成）

```java
@Override
public void execute(PlanType planType) {
    // 1. 查询所有生效的计划
    LambdaQueryWrapper<PlanDO> queryWrapper = planRepositoryService.queryWrapper(planType);
    queryWrapper.eq(PlanDO::getStatus, PlanStatusType.VALID.getCode());
    List<PlanDO> planDOS = planRepositoryService.list(planType, queryWrapper);
    
    for (PlanDO planDO : planDOS) {
        // 2. 获取计划的历史任务
        List<PlanTaskVO> taskVOS = planTaskService.getPlanBeforeTodayTask(planType, planDO.getId());
        
        // 3. 检查是否需要生成新任务
        if (!checkIsExpired(taskVOS, planDO)) {
            continue;
        }
        
        // 4. 将未完成的任务标记为已过期
        expiringTask(planType, taskVOS);
        
        // 5. 根据周期生成新的任务
        createTask(planType, planDO);
    }
}
```

### 5. PlanTaskService (计划任务服务)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/service/plan/PlanTaskService.java`

#### 5.1 主要方法

```java
public interface PlanTaskService {
    PageResponse<PlanTaskVO> paging(QueryRequest request);      // 分页查询任务
    Boolean execute(TaskExecuteRequest request);               // 执行任务
    Boolean create(PlanType planType, PlanDO planDO, List<Long> deviceIds); // 创建任务
    List<PlanTaskVO> getPlanBeforeTodayTask(PlanType planType, Long planId); // 获取历史任务
}
```

## 数据访问层 (Repository)

### 6. PatrolPlanRepositoryService (巡检计划数据访问服务)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/repository/PatrolPlanRepositoryService.java`

#### 6.1 接口定义

```java
public interface PatrolPlanRepositoryService extends IService<PatrolPlanDO>, PlanRepositoryService {
    boolean update(PlanRequest request);                        // 更新计划
    boolean abandon(PlanRequest request);                       // 作废计划
    LambdaQueryWrapper<PatrolPlanDO> queryWrapper();          // 查询构造器
    LambdaUpdateWrapper<PatrolPlanDO> updateWrapper();        // 更新构造器
}
```

### 7. PatrolPlanTaskRepositoryService (巡检计划任务数据访问服务)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/repository/PatrolPlanTaskRepositoryService.java`

#### 7.1 接口定义

```java
public interface PatrolPlanTaskRepositoryService extends IService<PatrolPlanTaskDO>, PlanTaskRepositoryService {
    LambdaQueryWrapper<PatrolPlanTaskDO> queryWrapper();      // 查询构造器
    Boolean execute(TaskExecuteRequest request);               // 执行任务
    Boolean expired(List<Long> taskIds);                       // 标记过期
    boolean update2delete(PlanRequest request);                // 逻辑删除
    Map<Integer, Integer> getStatisticsForYear(Integer year);  // 年度统计
    Map<Integer, Integer> getStatisticsForMonth(Integer month); // 月度统计
}
```

## 请求响应模型

### 8. 请求模型

#### 8.1 PlanRequest (计划请求模型)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/request/plan/PlanRequest.java`

```java
@Data
public class PlanRequest implements OperatorReq, Serializable {
    private Long id;                    // 主键ID
    private String code;                // 编号
    private String name;                // 名称
    private Integer circleType;         // 巡检周期类型（1-天，2-月）
    private Integer circle;             // 巡检周期
    private Integer status;             // 状态（1-生效，2-作废）
    private String remark;              // 备注
    private Long orgId;                 // 所属院区ID
    private List<Long> deviceIds;       // 关联设备ids
    private List<Long> orgIds;          // 关联部门id
    private List<Long> executorIds;     // 关联人员id
    private Operator operatorUser;      // 操作人
    private PlanType planType;          // 计划类型
}
```

#### 8.2 TaskExecuteRequest (任务执行请求模型)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/request/plan/TaskExecuteRequest.java`

```java
@Data
public class TaskExecuteRequest implements OperatorReq {
    private Long id;                                    // 任务ID
    private String remark;                              // 设备情况
    private Boolean isRepair;                           // 是否需要报修
    private PlanType planType;                          // 计划类型
    private RepairFormCreateRequest repairFormCreateRequest; // 报修单
    private Operator operatorUser;                      // 操作人
}
```

### 9. 响应模型

#### 9.1 PlanVO (计划响应模型)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/response/plan/PlanVO.java`

```java
@Data
public class PlanVO extends BaseVO implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;                    // 主键ID
    private String code;                // 编号
    private String name;                // 名称
    private Integer circleType;         // 巡检周期类型
    private Integer circle;             // 巡检周期
    private Integer status;             // 状态
    private String remark;              // 备注
    private Long orgId;                 // 所属院区ID
    private List<DeviceVO> deviceList;  // 关联设备列表
    private List<ExecutorVO> executorList; // 关联执行人员列表
    private List<OrgVO> orgList;        // 关联部门列表
    // ... 其他字段和时间字段
}
```

## 接口调用示例

### 10. 完整调用流程示例

#### 10.1 创建巡检计划

```bash
curl -X POST "http://localhost:8081/api/v1/web/project/plan/patrol/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "ICU设备日常巡检",
    "circleType": 1,
    "circle": 1,
    "remark": "ICU重要设备每日巡检",
    "orgId": 1001,
    "deviceIds": [1, 2, 3],
    "executorIds": [101, 102]
  }'
```

#### 10.2 查询巡检任务

```bash
curl -X GET "http://localhost:8081/api/v1/web/project/plan/patrol/task/paging?pageNum=1&pageSize=10&status=0" \
  -H "Authorization: Bearer your-token"
```

#### 10.3 执行巡检任务

```bash
curl -X PUT "http://localhost:8081/api/v1/web/project/plan/patrol/task/execute" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "id": 54321,
    "remark": "设备运行正常，温度、湿度均在正常范围内",
    "isRepair": false
  }'
```

## 总结

巡检计划后端接口采用了标准的三层架构：
- **Controller层**: 处理HTTP请求，参数验证，响应格式化
- **Service层**: 业务逻辑处理，事务管理
- **Repository层**: 数据访问，SQL操作

接口设计遵循RESTful规范，支持幂等性控制，提供了完整的CRUD操作和业务功能。通过合理的分层设计，保证了代码的可维护性和扩展性。

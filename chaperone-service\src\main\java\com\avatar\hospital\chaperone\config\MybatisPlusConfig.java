package com.avatar.hospital.chaperone.config;

import com.avatar.hospital.chaperone.tenant.handler.TenantHandler;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Plus配置 - 包含分页和多租户插件
 *
 * <AUTHOR>
 * @since 2023/10/11
 */
@Configuration
@RequiredArgsConstructor
public class MybatisPlusConfig {

    private final TenantHandler tenantHandler;

    /**
     * 配置MyBatis-Plus插件
     * 核心功能：自动为查询添加 where hid = ? 条件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 多租户插件 - 必须放在分页插件之前
        // 这是核心功能1：自动添加 where hid = ? 条件
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(tenantHandler));

        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));

        return interceptor;
    }
}

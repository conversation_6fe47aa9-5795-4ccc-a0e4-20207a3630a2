# Admin模块启动问题排查

## 🔍 问题现象

Admin模块启动失败，无法正常运行。

## 🔧 已修复的问题

### 1. 依赖版本缺失 ✅

**问题**: pom.xml中的依赖项缺少版本号，导致无法正确解析依赖。

**修复**: 为所有依赖项添加了正确的版本号：

```xml
<!-- 修复前 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>

<!-- 修复后 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>${mybatis-plus-boot-starter.version}</version>
</dependency>
```

### 2. 数据源配置问题 ✅

**问题**: Druid数据源配置结构不正确。

**修复**: 调整了数据源配置结构：

```yaml
# 修复前
datasource:
  type: com.alibaba.druid.pool.DruidDataSource
  druid:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://...
    username: root
    password: 123456

# 修复后
datasource:
  type: com.alibaba.druid.pool.DruidDataSource
  driver-class-name: com.mysql.cj.jdbc.Driver
  url: jdbc:mysql://...
  username: root
  password: 123456
  druid:
    # druid特定配置
```

### 3. Mapper扫描配置 ✅

**问题**: 缺少Mapper扫描配置。

**修复**: 在启动类中添加了@MapperScan注解：

```java
@SpringBootApplication
@MapperScan("com.avatar.hospital.chaperone.admin.mapper")
public class AdminApplication {
    // ...
}
```

## 🚀 启动步骤

### 1. 数据库准备

首先确保数据库已创建并执行初始化脚本：

```bash
# 创建数据库（如果不存在）
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS chaperone CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 执行初始化脚本
mysql -u root -p chaperone < chaperone-admin/src/main/resources/sql/admin_init.sql
```

### 2. 配置检查

检查 `application-dev.yaml` 配置：

```yaml
db:
  mysql: localhost      # 数据库地址
  username: root        # 数据库用户名
  password: 123456      # 数据库密码

redis:
  host: localhost       # Redis地址
  port: 6379           # Redis端口
  password:            # Redis密码（如果有）
  database: 2          # Redis数据库编号
```

### 3. 启动应用

```bash
# 方式1: 使用Maven启动
cd chaperone-admin
mvn spring-boot:run

# 方式2: 使用IDE启动
# 直接运行 AdminApplication.main() 方法
```

### 4. 验证启动

访问健康检查接口：
```bash
curl http://localhost:8081/admin/health
```

预期响应：
```json
{
    "code": 200,
    "message": "系统运行正常",
    "data": {
        "status": "UP",
        "timestamp": "2024-01-01T10:00:00",
        "application": "chaperone-admin",
        "version": "1.0.0"
    },
    "timestamp": 1704067200000
}
```

## 🔍 常见启动问题

### 1. 端口占用

**错误信息**: `Port 8081 was already in use`

**解决方案**:
```bash
# 查找占用端口的进程
netstat -ano | findstr :8081

# 杀死进程（替换PID）
taskkill /PID <PID> /F

# 或者修改端口
# 在application.yaml中修改server.port
```

### 2. 数据库连接失败

**错误信息**: `Communications link failure`

**解决方案**:
1. 检查数据库是否启动
2. 检查连接配置是否正确
3. 检查防火墙设置
4. 验证用户名密码

### 3. Redis连接失败

**错误信息**: `Unable to connect to Redis`

**解决方案**:
1. 检查Redis是否启动
2. 检查Redis配置
3. 如果不需要Redis，可以临时注释相关配置

### 4. 依赖冲突

**错误信息**: `ClassNotFoundException` 或 `NoSuchMethodError`

**解决方案**:
```bash
# 清理Maven缓存
mvn clean

# 重新下载依赖
mvn dependency:resolve

# 检查依赖树
mvn dependency:tree
```

## 🛠️ 调试技巧

### 1. 启用调试日志

在 `application-dev.yaml` 中添加：

```yaml
logging:
  level:
    root: DEBUG
    com.avatar.hospital.chaperone.admin: DEBUG
    org.springframework: DEBUG
```

### 2. 分步启动

如果启动失败，可以逐步排查：

1. **最小化配置**: 先注释掉数据库和Redis配置
2. **添加健康检查**: 确保基础Web功能正常
3. **逐步添加功能**: 依次启用数据库、Redis等功能

### 3. 使用Profile

创建不同环境的配置文件：

```yaml
# application-local.yaml - 本地开发环境
spring:
  datasource:
    url: jdbc:h2:mem:testdb  # 使用内存数据库
  h2:
    console:
      enabled: true
```

## 📋 启动检查清单

- [ ] **Java环境**: JDK 1.8+
- [ ] **Maven环境**: Maven 3.6+
- [ ] **数据库**: MySQL 8.0+ 已启动
- [ ] **Redis**: Redis 已启动（可选）
- [ ] **端口**: 8081端口未被占用
- [ ] **配置文件**: 数据库连接配置正确
- [ ] **初始化脚本**: 已执行数据库初始化脚本
- [ ] **依赖**: Maven依赖已正确下载

## 🎯 成功启动标志

当看到以下日志时，表示启动成功：

```
2024-01-01 10:00:00.000  INFO 12345 --- [main] c.a.h.c.admin.AdminApplication : Started AdminApplication in 5.123 seconds (JVM running for 6.456)
2024-01-01 10:00:00.000  INFO 12345 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer : Tomcat started on port(s): 8081 (http) with context path '/admin'
```

## 🔄 重启步骤

如果需要重启应用：

1. **停止应用**: Ctrl+C 或关闭IDE
2. **清理缓存**: `mvn clean`（可选）
3. **重新启动**: `mvn spring-boot:run`

## 📞 获取帮助

如果仍然无法启动，请提供以下信息：

1. **完整的错误日志**
2. **Java版本**: `java -version`
3. **Maven版本**: `mvn -version`
4. **数据库状态**: 是否能正常连接
5. **端口状态**: `netstat -ano | findstr :8081`

通过以上步骤，Admin模块应该能够成功启动！🚀

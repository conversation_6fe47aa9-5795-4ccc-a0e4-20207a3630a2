# 01-巡检计划数据库表结构与实体类文档

## 概述

本文档详细介绍了医院陪护系统中巡检计划相关的数据库表结构和对应的实体类，包括表的设计、字段说明、实体类定义以及相关枚举类型。

## 数据库表结构

### 1. 主要数据表

#### 1.1 t_project_patrol_plan (巡检计划表)

**表说明**: 存储巡检计划的基本信息

**字段结构**:
```sql
-- 基于 PlanDO 基础类的字段结构
id              BIGINT          主键ID，自增
code            VARCHAR(50)     编号
name            VARCHAR(100)    名称
circle_type     INT             巡检周期类型（1-天，2-月）
circle          INT             巡检周期
status          INT             状态（1-生效，2-作废）
remark          TEXT            备注
org_id          BIGINT          所属院区ID
create_by       BIGINT          创建者ID
update_by       BIGINT          更新者ID
created_at      DATETIME        创建时间
updated_at      DATETIME        更新时间
deleted         BIGINT          删除标记（1-未删除，其他-已删除时间戳）
```

#### 1.2 t_project_patrol_plan_task (巡检计划任务表)

**表说明**: 存储由巡检计划生成的具体任务

**字段结构**:
```sql
-- 基于 PlanTaskDO 基础类的字段结构
id              BIGINT          主键ID，自增
code            VARCHAR(50)     任务编号（字母+日期）
name            VARCHAR(100)    名称
status          INT             状态（0-未完成，1-已完成，2-已过期）
plan_id         BIGINT          计划ID
device_id       BIGINT          设备ID
is_repair       BOOLEAN         是否需要报修
remark          TEXT            备注
completed_time  DATETIME        完成时间
create_by       BIGINT          创建者ID
update_by       BIGINT          更新者ID
created_at      DATETIME        创建时间
updated_at      DATETIME        更新时间
deleted         BIGINT          删除标记
```

#### 1.3 t_project_patrol_plan_ref_device (巡检计划关联设备表)

**表说明**: 存储巡检计划与设备的关联关系

**字段结构**:
```sql
-- 基于 PlanRefDeviceDO 基础类的字段结构
id              BIGINT          主键ID，自增
plan_id         BIGINT          计划ID
device_id       BIGINT          设备ID
create_by       BIGINT          创建者ID
update_by       BIGINT          更新者ID
created_at      DATETIME        创建时间
updated_at      DATETIME        更新时间
deleted         BIGINT          删除标记
```

#### 1.4 t_project_patrol_plan_ref_executor (巡检计划关联人员表)

**表说明**: 存储巡检计划与执行人员的关联关系

**字段结构**:
```sql
-- 基于 PlanRefExecutorDO 基础类的字段结构
id              BIGINT          主键ID，自增
plan_id         BIGINT          计划ID
executor_id     BIGINT          执行人员ID
create_by       BIGINT          创建者ID
update_by       BIGINT          更新者ID
created_at      DATETIME        创建时间
updated_at      DATETIME        更新时间
deleted         BIGINT          删除标记
```

#### 1.5 t_project_patrol_plan_ref_org (巡检计划关联部门表)

**表说明**: 存储巡检计划与部门的关联关系

**字段结构**:
```sql
-- 基于 PlanRefOrgDO 基础类的字段结构
id              BIGINT          主键ID，自增
plan_id         BIGINT          计划ID
org_id          BIGINT          部门ID
create_by       BIGINT          创建者ID
update_by       BIGINT          更新者ID
created_at      DATETIME        创建时间
updated_at      DATETIME        更新时间
deleted         BIGINT          删除标记
```

## 实体类定义

### 2. 主要实体类

#### 2.1 PatrolPlanDO (巡检计划实体类)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/dataobject/PatrolPlanDO.java`

**类定义**:
```java
@Getter
@Setter
@TableName("t_project_patrol_plan")
public class PatrolPlanDO extends PlanDO {
    // 继承自 PlanDO 基础类，无额外字段
}
```

#### 2.2 PatrolPlanTaskDO (巡检计划任务实体类)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/dataobject/PatrolPlanTaskDO.java`

**类定义**:
```java
@Getter
@Setter
@TableName("t_project_patrol_plan_task")
public class PatrolPlanTaskDO extends PlanTaskDO {
    
    // 静态方法：生成任务名称
    public static String generateName(String planName, CircleType circleType, Integer num) {
        StringBuilder sb = new StringBuilder();
        sb.append(planName).append(circleType.getDescribe()).append("任务-");
        
        switch (circleType) {
            case DAY:
                sb.append(DateUtils.dateTimeStr(DAY_INT_PATTERN)).append("-").append(num);
                break;
            case MONTH:
                sb.append(DateUtils.monthStartStr()).append("-").append(num);
                break;
        }
        return sb.toString();
    }
}
```

#### 2.3 PatrolPlanRefDeviceDO (巡检计划关联设备实体类)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/dataobject/PatrolPlanRefDeviceDO.java`

**类定义**:
```java
@Getter
@Setter
@TableName("t_project_patrol_plan_ref_device")
public class PatrolPlanRefDeviceDO extends PlanRefDeviceDO {
    // 继承自 PlanRefDeviceDO 基础类，无额外字段
}
```

#### 2.4 PatrolPlanRefExecutorDO (巡检计划关联人员实体类)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/dataobject/PatrolPlanRefExecutorDO.java`

**类定义**:
```java
@Getter
@Setter
@TableName("t_project_patrol_plan_ref_executor")
public class PatrolPlanRefExecutorDO extends PlanRefExecutorDO {
    // 继承自 PlanRefExecutorDO 基础类，无额外字段
}
```

#### 2.5 PatrolPlanRefOrgDO (巡检计划关联部门实体类)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/dataobject/PatrolPlanRefOrgDO.java`

**类定义**:
```java
@Getter
@Setter
@TableName("t_project_patrol_plan_ref_org")
public class PatrolPlanRefOrgDO extends PlanRefOrgDO {
    // 继承自 PlanRefOrgDO 基础类，无额外字段
}
```

### 3. 基础实体类

#### 3.1 PlanDO (计划基础实体类)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/dataobject/base/PlanDO.java`

**主要字段**:
```java
@Getter
@Setter
public class PlanDO extends BaseDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;                    // 主键ID
    private String code;                // 编号
    private String name;                // 名称
    private Integer circleType;         // 巡检周期类型（1-天，2-月）
    private Integer circle;             // 巡检周期
    private Integer status;             // 状态（1-生效，2-作废）
    private String remark;              // 备注
    private Long orgId;                 // 所属院区ID
}
```

#### 3.2 PlanTaskDO (计划任务基础实体类)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/dataobject/base/PlanTaskDO.java`

**主要字段**:
```java
@Getter
@Setter
public class PlanTaskDO extends BaseDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;                    // 主键ID
    private String code;                // 任务编号（字母+日期）
    private String name;                // 名称
    private Integer status;             // 状态（0-未完成，1-已完成，2-已过期）
    private Long planId;                // 计划ID
    private Long deviceId;              // 设备ID
    private Boolean isRepair;           // 是否需要报修
    private String remark;              // 备注
    private LocalDateTime completedTime; // 完成时间
}
```

#### 3.3 BaseDO (通用基础实体类)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/dataobject/base/BaseDO.java`

**主要字段**:
```java
@Setter
@Getter
public class BaseDO implements Serializable {
    private Long createBy;              // 创建者ID
    private Long updateBy;              // 更新者ID
    private LocalDateTime createdAt;    // 创建时间
    private LocalDateTime updatedAt;    // 更新时间
    @TableField(select = false)
    private Long deleted;               // 删除标记（1-未删除，其他-已删除时间戳）
}
```

## 相关枚举类型

### 4. 枚举定义

#### 4.1 PlanType (计划类型枚举)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/enums/PlanType.java`

```java
@Getter
public enum PlanType {
    NONE(-1, "未知"),
    PATROL(1, "巡检"),
    MAINTENANCE(2, "维保");
    
    private final Integer code;
    private final String describe;
}
```

#### 4.2 CircleType (周期类型枚举)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/enums/CircleType.java`

```java
@Getter
public enum CircleType {
    NONE(-1, "未知"),
    DAY(1, "天"),
    MONTH(2, "月");
    
    private final Integer code;
    private final String describe;
}
```

#### 4.3 PlanStatusType (计划状态枚举)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/enums/PlanStatusType.java`

```java
@Getter
public enum PlanStatusType {
    NONE(-1, "未知"),
    VALID(1, "生效"),
    ABANDON(2, "作废");
    
    private final Integer code;
    private final String describe;
}
```

#### 4.4 TaskStatusType (任务状态枚举)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/database/plan/enums/TaskStatusType.java`

```java
@Getter
public enum TaskStatusType {
    NONE(-1, "未知"),
    NON_COMPLETED(0, "未完成"),
    COMPLETED(1, "已完成"),
    EXPIRED(2, "已过期");
    
    private final Integer code;
    private final String describe;
}
```

## 表关系说明

### 5. 数据表关系

1. **t_project_patrol_plan** (主表)
   - 一对多关联 **t_project_patrol_plan_task** (通过 plan_id)
   - 一对多关联 **t_project_patrol_plan_ref_device** (通过 plan_id)
   - 一对多关联 **t_project_patrol_plan_ref_executor** (通过 plan_id)
   - 一对多关联 **t_project_patrol_plan_ref_org** (通过 plan_id)

2. **t_project_patrol_plan_task** (任务表)
   - 多对一关联 **t_project_patrol_plan** (通过 plan_id)
   - 多对一关联 **t_project_device** (通过 device_id)

3. **关联表设计模式**
   - 采用中间表方式实现多对多关系
   - 支持计划与设备、人员、部门的灵活关联
   - 便于权限控制和数据查询

## 代码生成配置

### 6. MyBatis-Plus 代码生成

在 `GeneratorCodeTest.java` 中定义了相关表的代码生成配置：

```java
public static final String[] plan = {
    "t_project_maintenance_plan",
    "t_project_maintenance_plan_ref_device",
    "t_project_maintenance_plan_ref_executor", 
    "t_project_maintenance_plan_ref_org",
    "t_project_maintenance_plan_task",
    "t_project_patrol_plan",
    "t_project_patrol_plan_ref_device",
    "t_project_patrol_plan_ref_executor",
    "t_project_patrol_plan_ref_org",
    "t_project_patrol_plan_task"
};
```

## 总结

巡检计划模块采用了清晰的分层设计：
- **主表**: 存储计划基本信息
- **任务表**: 存储由计划生成的具体任务
- **关联表**: 实现计划与设备、人员、部门的多对多关系
- **基础类**: 提供通用字段和功能
- **枚举类**: 定义业务状态和类型

这种设计保证了数据结构的规范性和扩展性，为后续的业务逻辑实现提供了坚实的基础。

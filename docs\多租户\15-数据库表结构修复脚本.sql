-- 多租户数据库表结构修复脚本
-- 为需要租户隔离的表添加 hid 字段

-- 1. 巡检计划表
ALTER TABLE t_project_patrol_plan ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER remark;
ALTER TABLE t_project_patrol_plan ADD INDEX idx_hid (hid);
UPDATE t_project_patrol_plan SET hid = 1 WHERE hid IS NULL;

-- 2. 维护计划表
ALTER TABLE t_project_maintenance_plan ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER remark;
ALTER TABLE t_project_maintenance_plan ADD INDEX idx_hid (hid);
UPDATE t_project_maintenance_plan SET hid = 1 WHERE hid IS NULL;

-- 3. 项目设备表
ALTER TABLE t_project_device ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER remark;
ALTER TABLE t_project_device ADD INDEX idx_hid (hid);
UPDATE t_project_device SET hid = 1 WHERE hid IS NULL;

-- 4. 项目备件表
ALTER TABLE t_project_spare_part ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER remark;
ALTER TABLE t_project_spare_part ADD INDEX idx_hid (hid);
UPDATE t_project_spare_part SET hid = 1 WHERE hid IS NULL;

-- 5. 项目备件批次表
ALTER TABLE t_project_spare_part_batch ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER remark;
ALTER TABLE t_project_spare_part_batch ADD INDEX idx_hid (hid);
UPDATE t_project_spare_part_batch SET hid = 1 WHERE hid IS NULL;

-- 6. 备件库存申请表
ALTER TABLE t_project_spare_part_stock_apply ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER status;
ALTER TABLE t_project_spare_part_stock_apply ADD INDEX idx_hid (hid);
UPDATE t_project_spare_part_stock_apply SET hid = 1 WHERE hid IS NULL;

-- 7. 备件库存申请关联批次表
ALTER TABLE t_project_spare_part_stock_apply_ref_part_batch ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER updated_at;
ALTER TABLE t_project_spare_part_stock_apply_ref_part_batch ADD INDEX idx_hid (hid);
UPDATE t_project_spare_part_stock_apply_ref_part_batch SET hid = 1 WHERE hid IS NULL;

-- 8. 备件申请表
ALTER TABLE t_project_spare_part_apply ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER status;
ALTER TABLE t_project_spare_part_apply ADD INDEX idx_hid (hid);
UPDATE t_project_spare_part_apply SET hid = 1 WHERE hid IS NULL;

-- 9. 备件申请关联批次表
ALTER TABLE t_project_spare_part_apply_ref_part_batch ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER updated_at;
ALTER TABLE t_project_spare_part_apply_ref_part_batch ADD INDEX idx_hid (hid);
UPDATE t_project_spare_part_apply_ref_part_batch SET hid = 1 WHERE hid IS NULL;

-- 10. 护理表
ALTER TABLE t_nursing ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER status;
ALTER TABLE t_nursing ADD INDEX idx_hid (hid);
UPDATE t_nursing SET hid = 1 WHERE hid IS NULL;

-- 11. 护理医院关联表
ALTER TABLE t_nursing_hospital ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER updated_at;
ALTER TABLE t_nursing_hospital ADD INDEX idx_hid (hid);
UPDATE t_nursing_hospital SET hid = 1 WHERE hid IS NULL;

-- 12. 项目表
ALTER TABLE t_item ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER status;
ALTER TABLE t_item ADD INDEX idx_hid (hid);
UPDATE t_item SET hid = 1 WHERE hid IS NULL;

-- 13. 消息表
ALTER TABLE t_message ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER status;
ALTER TABLE t_message ADD INDEX idx_hid (hid);
UPDATE t_message SET hid = 1 WHERE hid IS NULL;

-- 14. 订单表
ALTER TABLE t_order ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER status;
ALTER TABLE t_order ADD INDEX idx_hid (hid);
UPDATE t_order SET hid = 1 WHERE hid IS NULL;

-- 15. 统计订单表
ALTER TABLE t_statistics_order ADD COLUMN hid BIGINT COMMENT '院区ID' AFTER updated_at;
ALTER TABLE t_statistics_order ADD INDEX idx_hid (hid);
UPDATE t_statistics_order SET hid = 1 WHERE hid IS NULL;

-- 验证脚本：检查所有表是否都有 hid 字段
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'chaperone' 
    AND COLUMN_NAME = 'hid'
    AND TABLE_NAME IN (
        't_project_patrol_plan',
        't_project_maintenance_plan', 
        't_project_device',
        't_project_spare_part',
        't_project_spare_part_batch',
        't_project_spare_part_stock_apply',
        't_project_spare_part_stock_apply_ref_part_batch',
        't_project_spare_part_apply',
        't_project_spare_part_apply_ref_part_batch',
        't_nursing',
        't_nursing_hospital',
        't_item',
        't_message',
        't_order',
        't_statistics_order'
    )
ORDER BY TABLE_NAME;

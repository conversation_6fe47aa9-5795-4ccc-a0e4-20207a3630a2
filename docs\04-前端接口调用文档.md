# 04-前端接口调用文档

## 概述

本文档详细介绍了前端应用如何调用医院陪护系统中巡检计划相关的后端接口，包括接口地址、请求方式、参数格式、响应处理以及完整的调用示例。

## 系统架构说明

### 1. 后端服务架构

```
医院陪护系统后端架构
├── chaperone-web-start (后台管理系统) - 端口: 8081
│   └── 提供管理员、护工调度员等后台用户接口
├── chaperone-consumer-start (客户端系统) - 端口: 8080
│   └── 提供患者、家属等终端用户接口
└── 共享数据库和Redis
```

### 2. 接口基础信息

**后台管理系统接口**:
- **基础URL**: `http://localhost:8081`
- **巡检计划接口前缀**: `/api/v1/web/project/plan/patrol`
- **巡检任务接口前缀**: `/api/v1/web/project/plan/patrol/task`

**客户端系统接口**:
- **基础URL**: `http://localhost:8080/api/v1/consumer`
- **主要用于**: 患者端查看巡检状态等

## 前端技术栈建议

### 3. 推荐技术栈

```javascript
// 推荐的前端技术栈
{
  "框架": "Vue.js 3.x / React 18.x",
  "HTTP客户端": "axios",
  "UI组件库": "Element Plus / Ant Design",
  "状态管理": "Pinia / Redux Toolkit",
  "路由": "Vue Router / React Router",
  "构建工具": "Vite / Create React App"
}
```

## 接口调用配置

### 4. Axios 配置示例

#### 4.1 基础配置

```javascript
// api/config.js
import axios from 'axios';

// 后台管理系统API配置
const webApi = axios.create({
  baseURL: 'http://localhost:8081',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 客户端API配置
const consumerApi = axios.create({
  baseURL: 'http://localhost:8080/api/v1/consumer',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

export { webApi, consumerApi };
```

#### 4.2 请求拦截器配置

```javascript
// api/interceptors.js
import { webApi } from './config';

// 请求拦截器 - 添加认证token
webApi.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一处理响应
webApi.interceptors.response.use(
  response => {
    const { data } = response;
    if (data.success) {
      return data.data;
    } else {
      throw new Error(data.message || '请求失败');
    }
  },
  error => {
    if (error.response?.status === 401) {
      // 未授权，跳转到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

## 巡检计划接口调用

### 5. 巡检计划管理接口

#### 5.1 创建巡检计划

```javascript
// api/patrol-plan.js
import { webApi } from './config';

/**
 * 创建巡检计划
 * @param {Object} planData 计划数据
 * @returns {Promise<number>} 返回计划ID
 */
export const createPatrolPlan = async (planData) => {
  try {
    const response = await webApi.post('/api/v1/web/project/plan/patrol/create', {
      name: planData.name,
      circleType: planData.circleType, // 1-天，2-月
      circle: planData.circle,
      remark: planData.remark,
      orgId: planData.orgId,
      deviceIds: planData.deviceIds,
      executorIds: planData.executorIds,
      orgIds: planData.orgIds
    });
    return response;
  } catch (error) {
    console.error('创建巡检计划失败:', error);
    throw error;
  }
};

// 使用示例
const handleCreatePlan = async () => {
  try {
    const planId = await createPatrolPlan({
      name: 'ICU设备日常巡检',
      circleType: 1,
      circle: 1,
      remark: 'ICU重要设备每日巡检',
      orgId: 1001,
      deviceIds: [1, 2, 3],
      executorIds: [101, 102]
    });
    console.log('计划创建成功，ID:', planId);
  } catch (error) {
    console.error('创建失败:', error.message);
  }
};
```

#### 5.2 查询巡检计划列表

```javascript
/**
 * 分页查询巡检计划
 * @param {Object} queryParams 查询参数
 * @returns {Promise<Object>} 分页数据
 */
export const getPatrolPlanList = async (queryParams) => {
  try {
    const response = await webApi.get('/api/v1/web/project/plan/patrol/paging', {
      params: {
        pageNum: queryParams.pageNum || 1,
        pageSize: queryParams.pageSize || 10,
        name: queryParams.name,
        status: queryParams.status,
        orgId: queryParams.orgId
      }
    });
    return response;
  } catch (error) {
    console.error('查询巡检计划失败:', error);
    throw error;
  }
};

// Vue 3 组合式API使用示例
import { ref, onMounted } from 'vue';

export default {
  setup() {
    const planList = ref([]);
    const loading = ref(false);
    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0
    });

    const loadPlanList = async () => {
      loading.value = true;
      try {
        const result = await getPatrolPlanList({
          pageNum: pagination.value.current,
          pageSize: pagination.value.pageSize
        });
        
        planList.value = result.list;
        pagination.value.total = result.total;
      } catch (error) {
        console.error('加载失败:', error);
      } finally {
        loading.value = false;
      }
    };

    onMounted(() => {
      loadPlanList();
    });

    return {
      planList,
      loading,
      pagination,
      loadPlanList
    };
  }
};
```

#### 5.3 查询巡检计划详情

```javascript
/**
 * 查询巡检计划详情
 * @param {number} planId 计划ID
 * @returns {Promise<Object>} 计划详情
 */
export const getPatrolPlanDetail = async (planId) => {
  try {
    const response = await webApi.get(`/api/v1/web/project/plan/patrol/detail/${planId}`);
    return response;
  } catch (error) {
    console.error('查询计划详情失败:', error);
    throw error;
  }
};

// React Hook 使用示例
import { useState, useEffect } from 'react';

const PatrolPlanDetail = ({ planId }) => {
  const [planDetail, setPlanDetail] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadPlanDetail = async () => {
      try {
        const detail = await getPatrolPlanDetail(planId);
        setPlanDetail(detail);
      } catch (error) {
        console.error('加载详情失败:', error);
      } finally {
        setLoading(false);
      }
    };

    if (planId) {
      loadPlanDetail();
    }
  }, [planId]);

  if (loading) return <div>加载中...</div>;
  if (!planDetail) return <div>暂无数据</div>;

  return (
    <div>
      <h2>{planDetail.name}</h2>
      <p>编号: {planDetail.code}</p>
      <p>周期: {planDetail.circle}{planDetail.circleType === 1 ? '天' : '月'}</p>
      <p>状态: {planDetail.status === 1 ? '生效' : '作废'}</p>
      <p>备注: {planDetail.remark}</p>
    </div>
  );
};
```

#### 5.4 更新巡检计划

```javascript
/**
 * 更新巡检计划
 * @param {Object} planData 计划数据（必须包含id）
 * @returns {Promise<boolean>} 更新结果
 */
export const updatePatrolPlan = async (planData) => {
  try {
    const response = await webApi.post('/api/v1/web/project/plan/patrol/update', planData);
    return response;
  } catch (error) {
    console.error('更新巡检计划失败:', error);
    throw error;
  }
};

// 表单提交示例
const handleUpdatePlan = async (formData) => {
  try {
    await updatePatrolPlan({
      id: formData.id,
      name: formData.name,
      circleType: formData.circleType,
      circle: formData.circle,
      remark: formData.remark,
      deviceIds: formData.deviceIds,
      executorIds: formData.executorIds
    });
    
    // 更新成功后的处理
    alert('更新成功');
    // 刷新列表或跳转页面
  } catch (error) {
    alert('更新失败: ' + error.message);
  }
};
```

#### 5.5 作废巡检计划

```javascript
/**
 * 作废巡检计划
 * @param {number} planId 计划ID
 * @returns {Promise<boolean>} 作废结果
 */
export const abandonPatrolPlan = async (planId) => {
  try {
    const response = await webApi.delete(`/api/v1/web/project/plan/patrol/abandon/${planId}`);
    return response;
  } catch (error) {
    console.error('作废巡检计划失败:', error);
    throw error;
  }
};

// 确认作废示例
const handleAbandonPlan = async (planId) => {
  if (confirm('确定要作废这个巡检计划吗？')) {
    try {
      await abandonPatrolPlan(planId);
      alert('作废成功');
      // 刷新列表
      loadPlanList();
    } catch (error) {
      alert('作废失败: ' + error.message);
    }
  }
};
```

## 巡检任务接口调用

### 6. 巡检任务管理接口

#### 6.1 查询巡检任务列表

```javascript
// api/patrol-task.js
import { webApi } from './config';

/**
 * 分页查询巡检任务
 * @param {Object} queryParams 查询参数
 * @returns {Promise<Object>} 分页数据
 */
export const getPatrolTaskList = async (queryParams) => {
  try {
    const response = await webApi.get('/api/v1/web/project/plan/patrol/task/paging', {
      params: {
        pageNum: queryParams.pageNum || 1,
        pageSize: queryParams.pageSize || 10,
        status: queryParams.status, // 0-未完成，1-已完成，2-已过期
        planId: queryParams.planId,
        deviceId: queryParams.deviceId
      }
    });
    return response;
  } catch (error) {
    console.error('查询巡检任务失败:', error);
    throw error;
  }
};

// 任务状态枚举
export const TASK_STATUS = {
  NON_COMPLETED: 0, // 未完成
  COMPLETED: 1,     // 已完成
  EXPIRED: 2        // 已过期
};

// 状态显示文本
export const getTaskStatusText = (status) => {
  const statusMap = {
    [TASK_STATUS.NON_COMPLETED]: '未完成',
    [TASK_STATUS.COMPLETED]: '已完成',
    [TASK_STATUS.EXPIRED]: '已过期'
  };
  return statusMap[status] || '未知';
};
```

#### 6.2 查询我的巡检任务

```javascript
/**
 * 查询当前用户的巡检任务
 * @param {Object} queryParams 查询参数
 * @returns {Promise<Object>} 分页数据
 */
export const getMyPatrolTasks = async (queryParams) => {
  try {
    const response = await webApi.get('/api/v1/web/project/plan/patrol/task/mine', {
      params: queryParams
    });
    return response;
  } catch (error) {
    console.error('查询我的巡检任务失败:', error);
    throw error;
  }
};

// Vue 3 我的任务页面示例
const MyTasks = {
  setup() {
    const myTasks = ref([]);
    const loading = ref(false);

    const loadMyTasks = async () => {
      loading.value = true;
      try {
        const result = await getMyPatrolTasks({
          pageNum: 1,
          pageSize: 20,
          status: TASK_STATUS.NON_COMPLETED // 只查询未完成的任务
        });
        myTasks.value = result.list;
      } catch (error) {
        console.error('加载我的任务失败:', error);
      } finally {
        loading.value = false;
      }
    };

    onMounted(() => {
      loadMyTasks();
    });

    return {
      myTasks,
      loading,
      loadMyTasks,
      getTaskStatusText
    };
  },
  
  template: `
    <div>
      <h2>我的巡检任务</h2>
      <div v-if="loading">加载中...</div>
      <div v-else>
        <div v-for="task in myTasks" :key="task.id" class="task-item">
          <h3>{{ task.name }}</h3>
          <p>状态: {{ getTaskStatusText(task.status) }}</p>
          <p>设备: {{ task.deviceName }}</p>
          <p>创建时间: {{ task.createdAt }}</p>
          <button @click="executeTask(task.id)">执行任务</button>
        </div>
      </div>
    </div>
  `
};
```

#### 6.3 执行巡检任务

```javascript
/**
 * 执行巡检任务
 * @param {Object} taskData 任务执行数据
 * @returns {Promise<boolean>} 执行结果
 */
export const executePatrolTask = async (taskData) => {
  try {
    const response = await webApi.put('/api/v1/web/project/plan/patrol/task/execute', {
      id: taskData.id,
      remark: taskData.remark,
      isRepair: taskData.isRepair,
      repairFormCreateRequest: taskData.repairFormCreateRequest
    });
    return response;
  } catch (error) {
    console.error('执行巡检任务失败:', error);
    throw error;
  }
};

// 任务执行表单组件
const TaskExecuteForm = {
  props: ['taskId'],
  setup(props, { emit }) {
    const form = ref({
      remark: '',
      isRepair: false,
      repairFormCreateRequest: null
    });

    const handleSubmit = async () => {
      try {
        await executePatrolTask({
          id: props.taskId,
          ...form.value
        });
        
        alert('任务执行成功');
        emit('success');
      } catch (error) {
        alert('执行失败: ' + error.message);
      }
    };

    return {
      form,
      handleSubmit
    };
  },
  
  template: `
    <form @submit.prevent="handleSubmit">
      <div>
        <label>巡检情况:</label>
        <textarea v-model="form.remark" placeholder="请描述设备巡检情况"></textarea>
      </div>
      <div>
        <label>
          <input type="checkbox" v-model="form.isRepair">
          需要报修
        </label>
      </div>
      <div v-if="form.isRepair">
        <!-- 报修表单组件 -->
        <repair-form-component v-model="form.repairFormCreateRequest" />
      </div>
      <button type="submit">提交</button>
    </form>
  `
};
```

## 完整页面示例

### 7. 巡检计划管理页面

#### 7.1 Vue 3 完整示例

```javascript
// pages/PatrolPlanManagement.vue
<template>
  <div class="patrol-plan-management">
    <div class="header">
      <h1>巡检计划管理</h1>
      <button @click="showCreateDialog = true">新建计划</button>
    </div>
    
    <!-- 搜索表单 -->
    <div class="search-form">
      <input v-model="searchForm.name" placeholder="计划名称">
      <select v-model="searchForm.status">
        <option value="">全部状态</option>
        <option value="1">生效</option>
        <option value="2">作废</option>
      </select>
      <button @click="handleSearch">搜索</button>
    </div>
    
    <!-- 计划列表 -->
    <div class="plan-list">
      <table>
        <thead>
          <tr>
            <th>编号</th>
            <th>名称</th>
            <th>周期</th>
            <th>状态</th>
            <th>创建时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="plan in planList" :key="plan.id">
            <td>{{ plan.code }}</td>
            <td>{{ plan.name }}</td>
            <td>{{ plan.circle }}{{ plan.circleType === 1 ? '天' : '月' }}</td>
            <td>{{ plan.status === 1 ? '生效' : '作废' }}</td>
            <td>{{ plan.createdAt }}</td>
            <td>
              <button @click="viewDetail(plan.id)">详情</button>
              <button @click="editPlan(plan)">编辑</button>
              <button @click="abandonPlan(plan.id)" v-if="plan.status === 1">作废</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- 分页 -->
    <div class="pagination">
      <button @click="prevPage" :disabled="pagination.current <= 1">上一页</button>
      <span>{{ pagination.current }} / {{ Math.ceil(pagination.total / pagination.pageSize) }}</span>
      <button @click="nextPage" :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)">下一页</button>
    </div>
    
    <!-- 创建/编辑对话框 -->
    <div v-if="showCreateDialog" class="dialog">
      <patrol-plan-form 
        :plan-data="editingPlan"
        @success="handleFormSuccess"
        @cancel="handleFormCancel"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { 
  getPatrolPlanList, 
  abandonPatrolPlan 
} from '@/api/patrol-plan';

export default {
  name: 'PatrolPlanManagement',
  setup() {
    const planList = ref([]);
    const loading = ref(false);
    const showCreateDialog = ref(false);
    const editingPlan = ref(null);
    
    const searchForm = ref({
      name: '',
      status: ''
    });
    
    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0
    });

    const loadPlanList = async () => {
      loading.value = true;
      try {
        const result = await getPatrolPlanList({
          pageNum: pagination.value.current,
          pageSize: pagination.value.pageSize,
          ...searchForm.value
        });
        
        planList.value = result.list;
        pagination.value.total = result.total;
      } catch (error) {
        console.error('加载失败:', error);
      } finally {
        loading.value = false;
      }
    };

    const handleSearch = () => {
      pagination.value.current = 1;
      loadPlanList();
    };

    const prevPage = () => {
      if (pagination.value.current > 1) {
        pagination.value.current--;
        loadPlanList();
      }
    };

    const nextPage = () => {
      const maxPage = Math.ceil(pagination.value.total / pagination.value.pageSize);
      if (pagination.value.current < maxPage) {
        pagination.value.current++;
        loadPlanList();
      }
    };

    const editPlan = (plan) => {
      editingPlan.value = { ...plan };
      showCreateDialog.value = true;
    };

    const abandonPlan = async (planId) => {
      if (confirm('确定要作废这个巡检计划吗？')) {
        try {
          await abandonPatrolPlan(planId);
          alert('作废成功');
          loadPlanList();
        } catch (error) {
          alert('作废失败: ' + error.message);
        }
      }
    };

    const handleFormSuccess = () => {
      showCreateDialog.value = false;
      editingPlan.value = null;
      loadPlanList();
    };

    const handleFormCancel = () => {
      showCreateDialog.value = false;
      editingPlan.value = null;
    };

    onMounted(() => {
      loadPlanList();
    });

    return {
      planList,
      loading,
      showCreateDialog,
      editingPlan,
      searchForm,
      pagination,
      handleSearch,
      prevPage,
      nextPage,
      editPlan,
      abandonPlan,
      handleFormSuccess,
      handleFormCancel
    };
  }
};
</script>
```

## 错误处理和最佳实践

### 8. 统一错误处理

```javascript
// utils/error-handler.js
export const handleApiError = (error) => {
  if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response;
    switch (status) {
      case 401:
        // 未授权
        localStorage.removeItem('token');
        window.location.href = '/login';
        break;
      case 403:
        alert('权限不足');
        break;
      case 500:
        alert('服务器内部错误');
        break;
      default:
        alert(data.message || '请求失败');
    }
  } else if (error.request) {
    // 网络错误
    alert('网络连接失败，请检查网络');
  } else {
    // 其他错误
    alert(error.message || '未知错误');
  }
};
```

### 9. 接口调用最佳实践

```javascript
// 1. 使用 loading 状态
const [loading, setLoading] = useState(false);

const handleApiCall = async () => {
  setLoading(true);
  try {
    const result = await apiFunction();
    // 处理成功结果
  } catch (error) {
    handleApiError(error);
  } finally {
    setLoading(false);
  }
};

// 2. 防抖处理搜索
import { debounce } from 'lodash';

const debouncedSearch = debounce((searchTerm) => {
  // 执行搜索
  loadPlanList();
}, 300);

// 3. 缓存常用数据
const cache = new Map();

const getCachedData = async (key, fetchFunction) => {
  if (cache.has(key)) {
    return cache.get(key);
  }
  
  const data = await fetchFunction();
  cache.set(key, data);
  return data;
};
```

## 总结

前端调用巡检计划接口的关键要点：

### 10. 核心要点

1. **统一配置**: 使用 axios 统一配置请求基础信息
2. **认证处理**: 在请求头中添加 Authorization token
3. **错误处理**: 统一处理各种错误情况
4. **状态管理**: 合理使用 loading 状态和数据缓存
5. **用户体验**: 提供友好的交互反馈

### 11. 接口调用流程

```
前端接口调用流程：
1. 用户操作触发
2. 显示 loading 状态
3. 发送 HTTP 请求
4. 处理响应结果
5. 更新页面状态
6. 隐藏 loading 状态
7. 显示操作结果
```

通过以上配置和示例，前端开发者可以轻松集成巡检计划相关功能，实现完整的巡检计划管理系统。

package com.avatar.hospital.chaperone.database.plan.repository;

import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanDO;
import com.avatar.hospital.chaperone.database.plan.repository.base.PlanRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 巡检计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PatrolPlanRepositoryService extends IService<PatrolPlanDO>, PlanRepositoryService {

    boolean update(PlanRequest request);

    boolean abandon(PlanRequest request);

    LambdaQueryWrapper<PatrolPlanDO> queryWrapper();

    LambdaUpdateWrapper<PatrolPlanDO> updateWrapper();

    /**
     * 查询所有计划（忽略租户隔离）
     *
     * @param queryWrapper
     * @return
     */
    List<PatrolPlanDO> selectAllWithoutTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper);
}

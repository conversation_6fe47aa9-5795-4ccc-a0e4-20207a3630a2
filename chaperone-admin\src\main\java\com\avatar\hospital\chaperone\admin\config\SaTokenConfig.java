package com.avatar.hospital.chaperone.admin.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * SA-Token配置
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，校验规则为StpUtil.checkLogin()登录校验
        registry.addInterceptor(new SaInterceptor(handle -> {
            SaRouter
                // 登录校验 -- 拦截所有路由，并排除/auth/login用于开放登录
                .match("/**")
                .notMatch("/auth/login")
                .notMatch("/auth/captcha")
                .notMatch("/health")
                .notMatch("/actuator/**")
                .notMatch("/druid/**")
                .notMatch("/swagger-ui/**")
                .notMatch("/v3/api-docs/**")
                .check(r -> StpUtil.checkLogin());
        })).addPathPatterns("/**");
    }

}

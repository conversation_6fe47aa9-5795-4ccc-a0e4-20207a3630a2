package com.avatar.hospital.chaperone.web.utils;

import cn.dev33.satoken.stp.StpUtil;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.baccount.repository.WebAccountRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.context.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户上下文初始化工具类 - 专注于初始化用户信息
 * 使用方式：任何地方直接调用 UserContext.currentUserId() 等静态方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CurrentUserUtils {

    private final WebAccountRepositoryService webAccountRepositoryService;

    /**
     * 初始化用户上下文（核心方法）
     * 一次请求只查询一次数据库，包含用户信息和院区信息
     */
    public void initUserContext() {
        // 如果已经初始化过，直接返回
        if (UserContext.exists()) {
            return;
        }

        // 检查用户是否登录
        if (!StpUtil.isLogin()) {
            log.debug("用户未登录，跳过初始化用户上下文");
            return;
        }

        try {
            // 查询用户信息
            Long userId = StpUtil.getLoginIdAsLong();
            AccountDO userDetail = webAccountRepositoryService.findById(userId);

            if (userDetail == null) {
                log.warn("用户不存在: userId={}", userId);
                return;
            }

            // 构建并设置用户上下文
            UserContext context = UserContext.builder()
                    .userId(userDetail.getId())
                    .nickname(userDetail.getNickname())
                    .phoneNumber(userDetail.getPhoneNumber())
                    .username(userDetail.getNickname()) // 使用昵称作为用户名
                    .email(userDetail.getPhoneNumber() + "@hospital.com") // 临时邮箱
                    .hospitalId(userDetail.getHid()) // 院区ID - 租户信息
                    .isAdmin(userDetail.admin())
                    .build();

            UserContext.set(context);
            log.debug("初始化用户上下文成功: userId={}, hospitalId={}", userId, userDetail.getHid());

        } catch (Exception e) {
            log.error("初始化用户上下文失败", e);
        }
    }

    /**
     * 清除用户上下文缓存
     */
    public static void clearUserContext() {
        UserContext.clear();
    }
}


package com.avatar.hospital.chaperone.service.baccount.impl;

import com.avatar.hospital.chaperone.builder.baccount.WebAccountBuilder;
import com.avatar.hospital.chaperone.component.baccount.OrganizationComponent;
import com.avatar.hospital.chaperone.component.baccount.RoleComponent;
import com.avatar.hospital.chaperone.component.baccount.StpComponent;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountOrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleDO;
import com.avatar.hospital.chaperone.database.baccount.enums.OrganizationType;
import com.avatar.hospital.chaperone.database.baccount.repository.OrganizationRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.WebAccountRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.WebAccountAddRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountAllocationRoleRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountPagingRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountUpdatePasswordRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountAddResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountAllocationRoleResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountBasicResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountPagingResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountUpdatePasswordResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountUpdateResponse;
import com.avatar.hospital.chaperone.service.baccount.WebAccountService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.PasswordUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WebAccountServiceImpl implements WebAccountService {

    private final WebAccountRepositoryService webAccountRepositoryService;
    private final OrganizationRepositoryService organizationRepositoryService;
    private final RoleRepositoryService roleRepositoryService;
    private final StpComponent stpComponent;
    private final OrganizationComponent organizationComponent;
    private final RoleComponent roleComponent;

    @Override
    public WebAccountAddResponse add(WebAccountAddRequest request) {

        AccountDO accountDO = webAccountRepositoryService.findByPhoneNumberAndHid(request.getPhoneNumber(), request.getHospitalId());
        AssertUtils.isNull(accountDO, ErrorCode.WEB_ACCOUNT_PHONE_NUMBER_EXIST);

        Set<Long> organizationIds = Sets.newLinkedHashSet();
        // 医院
//        if (CollectionUtils.isNotEmpty(request.getOrganizationIds())) {
//            List<OrganizationDO> organizationList = organizationRepositoryService.findByIds(request.getOrganizationIds());
//            AssertUtils.notEmpty(organizationList, ErrorCode.ORGANIZATION_NOT_EXIST);
//            organizationIds.addAll(organizationList.stream().map(OrganizationDO::getId).collect(Collectors.toSet()));
//        }

        // 部门
        if (CollectionUtils.isNotEmpty(request.getDepartmentIds())) {
            List<OrganizationDO> organizationList = organizationRepositoryService.findByIds(request.getDepartmentIds());
            AssertUtils.notEmpty(organizationList, ErrorCode.ORGANIZATION_NOT_EXIST);
            organizationIds.addAll(organizationList.stream().map(OrganizationDO::getId).collect(Collectors.toSet()));
        }

        List<AccountOrganizationDO> accountOrganizationList = Lists.newLinkedList();

        organizationIds.forEach(organizationId -> {
            AccountOrganizationDO accountOrganizationDO = new AccountOrganizationDO();
            accountOrganizationDO.setOrganizationId(organizationId);
            accountOrganizationList.add(accountOrganizationDO);
        });

        Long accountId = webAccountRepositoryService.add(WebAccountBuilder.buildAccountDO(request), accountOrganizationList);
        return WebAccountAddResponse.builder().accountId(accountId).build();
    }

    @Override
    public WebAccountUpdateResponse update(WebAccountUpdateRequest request) {
        AccountDO accountDO = webAccountRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(accountDO, ErrorCode.WEB_ACCOUNT_NOT_EXIST);

        if (!Objects.equals(request.getPhoneNumber(), accountDO.getPhoneNumber())) {
            AccountDO account = webAccountRepositoryService.findByPhoneNumberAndHid(request.getPhoneNumber(), accountDO.getHid());
            AssertUtils.isNull(account, ErrorCode.WEB_ACCOUNT_PHONE_NUMBER_EXIST);
        }

        Set<Long> organizationIds = Sets.newLinkedHashSet();
        // 医院
//        if (CollectionUtils.isNotEmpty(request.getOrganizationIds())) {
//            List<OrganizationDO> organizationList = organizationRepositoryService.findByIds(request.getOrganizationIds());
//            AssertUtils.notEmpty(organizationList, ErrorCode.ORGANIZATION_NOT_EXIST);
//            organizationIds.addAll(organizationList.stream().map(OrganizationDO::getId).collect(Collectors.toSet()));
//        }
        // 部门
        if (CollectionUtils.isNotEmpty(request.getDepartmentIds())) {
            List<OrganizationDO> organizationList = organizationRepositoryService.findByIds(request.getDepartmentIds());
            AssertUtils.notEmpty(organizationList, ErrorCode.ORGANIZATION_NOT_EXIST);
            organizationIds.addAll(organizationList.stream().map(OrganizationDO::getId).collect(Collectors.toSet()));
        }

        List<AccountOrganizationDO> accountOrganizationList = Lists.newLinkedList();
        organizationIds.forEach(organization -> {
            AccountOrganizationDO accountOrganizationDO = new AccountOrganizationDO();
            accountOrganizationDO.setOrganizationId(organization);
            accountOrganizationDO.setAccountId(request.getId());
            accountOrganizationList.add(accountOrganizationDO);
        });

        Boolean result = webAccountRepositoryService.incrementUpdate(WebAccountBuilder.buildAccountDO(request), accountOrganizationList);
        AssertUtils.isTrue(result, ErrorCode.UPDATE_ERROR);
        return WebAccountUpdateResponse.builder().success(true).build();
    }

    @Override
    public WebAccountDeleteResponse delete(WebAccountDeleteRequest request) {
        List<AccountDO> accountList = webAccountRepositoryService.findByIds(request.getIds());
        if (CollectionUtils.isEmpty(accountList)) {
            return WebAccountDeleteResponse.builder().success(true).build();
        }
        Set<Long> accountIds = accountList.stream().map(AccountDO::getId).collect(Collectors.toSet());
        Boolean result = webAccountRepositoryService.deleteByIds(accountIds, request.getUpdateBy());
        // 删除用户缓存信息
        if (Boolean.TRUE.equals(result)) {
            stpComponent.deleteUserRoleCache(accountIds);
        }
        return WebAccountDeleteResponse.builder().success(result).build();
    }

    @Override
    public WebAccountUpdatePasswordResponse changePassword(WebAccountUpdatePasswordRequest request) {
        AccountDO accountDO = webAccountRepositoryService.findById(request.getAccountId());
        AssertUtils.isNotNull(accountDO, ErrorCode.WEB_ACCOUNT_NOT_EXIST);

        Boolean result = webAccountRepositoryService.changePassword(request.getAccountId(), PasswordUtils.encrypt(request.getNewPassword()), request.getUpdateBy());
        AssertUtils.isTrue(result, ErrorCode.UPDATE_ERROR);
        return WebAccountUpdatePasswordResponse.builder().success(true).build();
    }

    @Override
    public WebAccountAllocationRoleResponse allocationRole(WebAccountAllocationRoleRequest request) {
        AccountDO accountDO = webAccountRepositoryService.findById(request.getAccountId());
        AssertUtils.isNotNull(accountDO, ErrorCode.WEB_ACCOUNT_NOT_EXIST);

        List<RoleDO> roleList = roleRepositoryService.findByIds(request.getRoleIds());
        AssertUtils.notEmpty(roleList, ErrorCode.ROLE_NOT_EXIST);

        Set<Long> roleIds = roleList.stream().map(RoleDO::getId).collect(Collectors.toSet());
        Boolean result = webAccountRepositoryService.allocationRole(request.getAccountId(), roleIds);
        // 删除用户缓存信息
        if (Boolean.TRUE.equals(result)) {
            stpComponent.deleteUserRoleCache(accountDO.getId());
        }
        return WebAccountAllocationRoleResponse.builder().success(result).build();
    }

    @Override
    public PageResponse<WebAccountPagingResponse> paging(WebAccountPagingRequest request) {
        PageResponse<AccountDO> accountPageResponse = webAccountRepositoryService.paging(request.getPageIndex(), request.getPageSize(), WebAccountBuilder.buildAccountDO(request));
        return WebAccountBuilder.buildAccountPagingResponse(accountPageResponse);
    }

    @Override
    public WebAccountDetailResponse detail(Long accountId) {
        AccountDO accountDO = webAccountRepositoryService.findById(accountId);
        AssertUtils.isNotNull(accountDO, ErrorCode.WEB_ACCOUNT_NOT_EXIST);

        List<OrganizationDO> organizationDOList = organizationRepositoryService.findByIds(organizationComponent.findAccountOrganizationIds(accountId));

        // List<OrganizationDO> organizationList = organizationDOList.stream().filter(organizationDO -> OrganizationType.HOSPITAL.getType().equals(organizationDO.getType())).collect(Collectors.toList());
//        List<OrganizationDO> departmentList = organizationDOList.stream().filter(
//                organizationDO -> OrganizationType.DEPARTMENT.getType().equals(organizationDO.getType())).collect(Collectors.toList());

        List<RoleDO> roleDOList = roleComponent.findAccountRoleList(accountId);

        return WebAccountBuilder.buildAccountDetailResponse(accountDO, organizationDOList, roleDOList);
    }

    @Override
    public Map<Long, String> getAccountMap(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Maps.newHashMap();
        }
        return webAccountRepositoryService.findMapByIdList(Lists.newArrayList(ids));
    }

    @Override
    public WebAccountBasicResponse basicInfo(Long accountId) {
        AccountDO accountDO = webAccountRepositoryService.findById(accountId);
        AssertUtils.isNotNull(accountDO, ErrorCode.WEB_ACCOUNT_NOT_EXIST);
        return WebAccountBuilder.buildAccountBasicResponse(accountDO);
    }
}

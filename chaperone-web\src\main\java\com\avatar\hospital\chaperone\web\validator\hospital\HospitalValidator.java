package com.avatar.hospital.chaperone.web.validator.hospital;


import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.hospital.HospitalAddRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalPagingRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalUpdateRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;

/**
 * 院区验证器
 */

public class HospitalValidator {

    // 新增验证
    public static void addValidator(HospitalAddRequest request) {
        // 设置创建人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setCreateBy(accountId);
    }

    // 更新
    public static void updateValidator(HospitalUpdateRequest request) {
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    // 分页
    public static void pagingValidate(HospitalPagingRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageIndex(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageSize(), ErrorCode.PARAMETER_ERROR);
    }

}

package com.avatar.hospital.chaperone.admin.service;

import com.avatar.hospital.chaperone.admin.entity.AdminUser;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 超级管理员用户服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AdminUserService extends IService<AdminUser> {

    /**
     * 根据用户名查询管理员用户
     * 
     * @param username 用户名
     * @return 管理员用户
     */
    AdminUser getByUsername(String username);

    /**
     * 根据手机号查询管理员用户
     * 
     * @param phone 手机号
     * @return 管理员用户
     */
    AdminUser getByPhone(String phone);

    /**
     * 验证密码
     * 
     * @param rawPassword 原始密码
     * @param encodedPassword 加密密码
     * @return 验证结果
     */
    boolean verifyPassword(String rawPassword, String encodedPassword);

    /**
     * 加密密码
     * 
     * @param rawPassword 原始密码
     * @return 加密密码
     */
    String encodePassword(String rawPassword);

    /**
     * 更新最后登录信息
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @return 更新结果
     */
    boolean updateLastLoginInfo(Long userId, String loginIp);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Long excludeId);

    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isPhoneExists(String phone, Long excludeId);

}

server:
  port: 8181
  tomcat:
    uri-encoding: UTF-8

spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB
  profiles:
    active: ${ENV:dev}

db:
  mysql: ${DB_MYSQL_URL:127.0.0.1}
  redis: ${DB_REDIS_URL:127.0.0.1}
  redis-port: ${DB_REDIS_PORT:6379}

role:
  hospitalListRoleKey: hospitalList
  departmentListRoleKey: departmentList
  organizationTreeRoleKey: organizationTree
  mkdirRoleKey: knowledgeBaseMkdir

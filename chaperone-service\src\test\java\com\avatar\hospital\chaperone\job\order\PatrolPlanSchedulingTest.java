package com.avatar.hospital.chaperone.job.order;

import com.avatar.hospital.chaperone.TestApplication;
import com.avatar.hospital.chaperone.job.plan.MaintenancePlanScheduling;
import com.avatar.hospital.chaperone.job.plan.PatrolPlanScheduling;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2024-01-03 16:59
 **/
@SpringBootTest(classes = TestApplication.class)
public class PatrolPlanSchedulingTest {
    @Autowired
    private PatrolPlanScheduling patrolPlanScheduling;

    @Autowired
    private MaintenancePlanScheduling maintenancePlanScheduling;

    // 巡检
    @Test
    public void testExecute() {
        patrolPlanScheduling.execute(20250718, 20);
    }

    // 维保
    @Test
    public void testExecute2() {
        maintenancePlanScheduling.execute(20250720, 17);
    }

}

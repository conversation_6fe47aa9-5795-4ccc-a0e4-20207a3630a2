package com.avatar.hospital.chaperone.service.hospital.impl;

import com.avatar.hospital.chaperone.builder.hospital.HospitalBuilder;
import com.avatar.hospital.chaperone.database.hospital.dataobject.HospitalDO;
import com.avatar.hospital.chaperone.database.hospital.repository.HospitalRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.hospital.HospitalAddRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalDeleteRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalPagingRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.hospital.*;
import com.avatar.hospital.chaperone.service.hospital.HospitalService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class HospitalServiceImpl implements HospitalService {

    @Autowired
    private HospitalRepositoryService hospitalRepositoryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HospitalAddResponse add(HospitalAddRequest request) {

        HospitalDO hospitalDO = new HospitalDO();
        hospitalDO.setAddress(request.getAddress());
        hospitalDO.setName(request.getName());
        hospitalDO.setRemark(request.getRemark());
        hospitalDO.setEnable(request.getEnable());
        hospitalDO.setCreateBy(request.getCreateBy());
        hospitalDO.setUpdateBy(request.getCreateBy());
        hospitalDO.setCreatedAt(LocalDateTime.now());
        hospitalDO.setUpdatedAt(LocalDateTime.now());
        hospitalDO.setDeleted(DelUtils.NO_DELETED);
        Long id = hospitalRepositoryService.add(hospitalDO);
        return HospitalAddResponse.builder().id(id).build();
    }

    /**
     * 更新
     */
    @Override
    public HospitalUpdateResponse update(HospitalUpdateRequest request) {
        HospitalDO data = hospitalRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(data, ErrorCode.NURSING_NOT_EXIST);
        data.setAddress(request.getAddress());
        data.setName(request.getName());
        data.setRemark(request.getRemark());
        data.setEnable(request.getEnable());
        data.setUpdateBy(request.getUpdateBy());
        data.setUpdatedAt(LocalDateTime.now());
        Boolean result = hospitalRepositoryService.updateById(data);
        return HospitalUpdateResponse.builder().success(result).build();
    }

    @Override
    public PageResponse<HospitalPagingResponse> paging(HospitalPagingRequest request) {
        PageResponse<HospitalDO> paging = hospitalRepositoryService.paging(request);
        return HospitalBuilder.buildPagingDO(paging);
    }

    @Override
    public HospitalDeleteResponse delete(HospitalDeleteRequest request) {
        return null;
    }

    /**
     * 选择框数据
     */
    @Override
    public List<HospitalResourceDataResponse> getResourceData() {
        List<HospitalDO> resourceData = hospitalRepositoryService.getResourceData();
        return HospitalBuilder.buildResourceData(resourceData);
    }

}

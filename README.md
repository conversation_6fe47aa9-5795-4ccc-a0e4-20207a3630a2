# 医院护工管理系统 (Hospital Chaperone)

本项目是一个基于Spring Boot的医院护工管理系统，采用多模块架构设计，支持后台管理系统和面向客户的接口服务。

## 项目架构

项目采用分层多模块架构，主要包含以下模块：

```
hospital-chaperone (根项目)
├── chaperone-service (核心服务层)
├── chaperone-web (后台管理接口层)
├── chaperone-web-start (后台管理启动模块)
├── chaperone-consumer (客户端接口层)
├── chaperone-consumer-start (客户端启动模块)
└── document (项目文档和SQL脚本)
```

## 模块详解与调用关系

### 1. chaperone-service（核心服务层）

**职责**：
- 定义领域模型与数据传输对象(DTO)
- 提供业务逻辑实现
- 封装数据访问操作
- 定义公共工具类和服务接口

**核心组件**：
- `com.avatar.hospital.chaperone.service`: 服务接口定义
- `com.avatar.hospital.chaperone.service.impl`: 服务接口实现
- `com.avatar.hospital.chaperone.database`: 数据库相关组件
  - `.baccount`: 后台账户管理相关
  - `.caccount`: 客户账户管理相关
  - `.item`: 项目管理相关
  - `.nursing`: 护理管理相关
  - `.order`: 订单管理相关
- `com.avatar.hospital.chaperone.request`: 请求参数定义
- `com.avatar.hospital.chaperone.response`: 响应参数定义
- `com.avatar.hospital.chaperone.utils`: 工具类
- `com.avatar.hospital.chaperone.config`: 配置类

**关键特性**：
- 作为所有业务逻辑的中心
- 不包含任何Web层或表示层代码
- 被web和consumer模块共同依赖
- 实现了公共的数据库交互逻辑
- 包含各种服务实现，如用户认证、订单管理等

**调用示例**：
```java
// 服务接口定义
public interface WebLoginService {
    PhoneNumberPasswordWebLoginResponse login(PhoneNumberPasswordWebLoginRequest request);
}

// 服务实现
@Service
public class WebLoginServiceImpl implements WebLoginService {
    private final WebAccountRepositoryService webAccountRepositoryService;
    
    @Override
    public PhoneNumberPasswordWebLoginResponse login(PhoneNumberPasswordWebLoginRequest request) {
        // 验证用户名密码
        AccountDO accountDO = webAccountRepositoryService.findByPhoneNumber(request.getPhoneNumber());
        // 执行登录逻辑...
        return PhoneNumberPasswordWebLoginResponse.builder()
            .tokenName(tokenInfo.getTokenName())
            .tokenValue(tokenInfo.getTokenValue())
            .build();
    }
}
```

### 2. chaperone-web（后台管理接口层）

**职责**：
- 定义后台管理系统的REST API接口
- 处理请求参数校验
- 调用service层服务
- 处理异常和响应封装

**核心组件**：
- `com.avatar.hospital.chaperone.web.controller`: 控制器定义
  - `.baccount`: 后台账户相关控制器
  - `.item`: 项目管理相关控制器
  - `.nursing`: 护理相关控制器
  - `.order`: 订单相关控制器
- `com.avatar.hospital.chaperone.web.validator`: 参数验证器
- `com.avatar.hospital.chaperone.web.handler`: 全局异常处理
- `com.avatar.hospital.chaperone.web.configurer`: Web配置

**关键特性**：
- 仅包含后台管理相关的接口
- 依赖chaperone-service模块获取业务功能
- 主要为管理员、护工调度员等后台用户提供服务
- 提供了REST风格的API接口
- 集成了Sa-Token进行权限管理

**调用示例**：
```java
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/login")
public class WebLoginController {
    private final WebLoginService webLoginService;
    
    @PostMapping(value = "")
    public SingleResponse<PhoneNumberPasswordWebLoginResponse> login(@RequestBody PhoneNumberPasswordWebLoginRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("WebLoginController login request:{}", JSON.toJSONString(request));
            WebLoginValidator.loginValidate(request);
            return webLoginService.login(request);
        });
    }
}
```

### 3. chaperone-web-start（后台管理启动模块）

**职责**：
- 提供应用程序入口点
- 包含应用配置文件
- 整合所有组件并启动应用
- 配置数据源和应用环境

**核心组件**：
- `com.avatar.hospital.chaperone.web.HospitalChaperoneWebApplication`: 主启动类
- `resources/config/application.yaml`: 主配置文件
- `resources/config/application-dev.yaml`: 开发环境配置
- `resources/config/application-prod.yaml`: 生产环境配置

**关键特性**：
- 依赖chaperone-web模块
- 配置了8081端口运行
- 包含数据库、Redis、微信等各种集成配置
- 提供开发和生产环境的不同配置
- 配置了MyBatis-Plus等持久化框架

**启动类**：
```java
@ComponentScan("com.avatar.hospital.chaperone")
@MapperScans(value = {
        @MapperScan(value = "com.avatar.hospital.chaperone.database.baccount.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.caccount.mapper"),
        // 其他mapper扫描...
})
@SpringBootApplication
@EnableScheduling
public class HospitalChaperoneWebApplication {
    public static void main(String[] args) {
        SpringApplication.run(HospitalChaperoneWebApplication.class, args);
    }
}
```

### 4. chaperone-consumer（客户端接口层）

**职责**：
- 定义面向客户的REST API接口
- 处理客户端请求验证
- 调用service层服务
- 封装客户端响应

**核心组件**：
- `com.avatar.hospital.chaperone.consumer.controller`: 客户端控制器
  - `.caccount`: 客户账户相关控制器
  - `.order`: 订单相关控制器
  - `.nursing`: 护理服务相关控制器
- `com.avatar.hospital.chaperone.consumer.validator`: 参数验证器 
- `com.avatar.hospital.chaperone.consumer.handler`: 全局异常处理
- `com.avatar.hospital.chaperone.consumer.configurer`: 客户端配置

**关键特性**：
- 提供面向终端用户（如患者、家属）的接口
- 依赖chaperone-service模块
- 主要为普通用户提供服务预订、状态查询等功能
- 提供了微信小程序/公众号集成接口
- 集成了支付功能

**调用示例**：
```java
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/login")
public class ConsumerLoginController {
    private final ConsumerLoginService consumerLoginService;
    
    @PostMapping(value = "")
    public SingleResponse<ConsumerAccountLoginResponse> login(@RequestBody ConsumerAccountLoginRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("LoginController login request:{}", JSON.toJSONString(request));
            LoginValidator.loginValidate(request);
            return consumerLoginService.login(request);
        });
    }
}
```

### 5. chaperone-consumer-start（客户端启动模块）

**职责**：
- 提供客户端应用程序入口点
- 包含客户端应用配置文件
- 整合所有客户端组件并启动
- 配置客户端数据源和环境

**核心组件**：
- `com.avatar.hospital.chaperone.consumer.HospitalChaperoneConsumerApplication`: 主启动类
- `resources/config/application.yaml`: 主配置文件
- `resources/config/application-dev.yaml`: 开发环境配置
- `resources/config/application-prod.yaml`: 生产环境配置

**关键特性**：
- 依赖chaperone-consumer模块
- 配置了8080端口运行
- 配置了/api/v1/consumer的上下文路径
- 包含与微信集成的配置
- 配置了支付相关组件

## 模块间的依赖关系

依赖关系图：

```
                    +----------------+
                    | parent (pom)   |
                    +----------------+
                            |
                            v
           +----------------+----------------+
           |                                 |
           v                                 v
+--------------------+             +--------------------+
| chaperone-service  |<------------| chaperone-service  |
+--------------------+             +--------------------+
           ^                                 ^
           |                                 |
           v                                 v
+--------------------+             +--------------------+
| chaperone-web      |             | chaperone-consumer |
+--------------------+             +--------------------+
           ^                                 ^
           |                                 |
           v                                 v
+--------------------+             +--------------------+
| chaperone-web-start|             |chaperone-consumer- |
+--------------------+             |      start         |
                                   +--------------------+
```

## 调用流程

### 1. 后台管理系统的调用流程

以登录功能为例：

```
HTTP请求 
  │
  ▼
chaperone-web-start (启动模块)
  │
  ▼
chaperone-web (WebLoginController)
  │ ├─ 请求参数验证 (WebLoginValidator)
  │ ├─ 异常处理 (WebGlobalExceptionHandler)
  │ └─ 接口权限验证 (SaTokenConfigure)
  │
  ▼
chaperone-service (WebLoginService)
  │ ├─ 业务逻辑处理 (WebLoginServiceImpl)
  │ ├─ 数据库操作 (WebAccountRepositoryService)
  │ └─ 用户认证 (Sa-Token)
  │
  ▼
数据库、Redis等
```

### 2. 客户端系统的调用流程

以客户登录为例：

```
HTTP请求 
  │
  ▼
chaperone-consumer-start (启动模块)
  │
  ▼
chaperone-consumer (ConsumerLoginController)
  │ ├─ 请求参数验证 (LoginValidator)
  │ ├─ 异常处理 (ConsumerGlobalExceptionHandler)
  │ └─ 接口权限验证 (SaTokenConfigure)
  │
  ▼
chaperone-service (ConsumerLoginService)
  │ ├─ 业务逻辑处理 (ConsumerLoginServiceImpl)
  │ ├─ 数据库操作 (ConsumerAccountRepositoryService)
  │ └─ 用户认证 (Sa-Token)
  │
  ▼
数据库、Redis等
```

## 技术栈

项目主要使用以下技术栈：

- **基础框架**: Spring Boot 2.7.6
- **ORM框架**: MyBatis-Plus 3.5.2
- **数据库**: MySQL 8.0.15
- **缓存**: Redis (通过Redisson 3.20.1)
- **认证授权**: Sa-Token 1.36.0
- **微信集成**: WxJava 4.5.0
- **对象存储**: 阿里云OSS
- **Excel处理**: EasyExcel 3.2.1
- **JSON处理**: FastJSON 1.2.83
- **工具库**: Apache Commons、Guava
- **项目结构**: 多模块Maven项目

## 运行说明

### 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 5.0+

### 数据库配置

1. 创建数据库:
   ```sql
   CREATE DATABASE chaperone DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
   ```

2. 导入初始SQL:
   ```
   mysql -u root -p chaperone < document/sql/b_account.sql
   mysql -u root -p chaperone < document/sql/c_account.sql
   mysql -u root -p chaperone < document/sql/ruoyi.sql
   ```

### 配置修改

修改以下配置文件中的数据库和Redis连接信息:

1. 后台管理配置:
   - `chaperone-web-start/src/main/resources/config/application-dev.yaml`

2. 客户端配置:
   - `chaperone-consumer-start/src/main/resources/config/application-dev.yaml`

### 编译打包

```bash
# 在项目根目录运行
mvn clean package -DskipTests
```

### 启动服务

1. 启动后台管理系统:
   ```bash
   cd chaperone-web-start/target
   java -jar chaperone-web-start-1.0.0-SNAPSHOT.jar
   ```

2. 启动客户端系统:
   ```bash
   cd chaperone-consumer-start/target
   java -jar chaperone-consumer-start-1.0.0-SNAPSHOT.jar
   ```

### 访问地址

- 后台管理API: `http://localhost:8081`
  - 登录接口: `http://localhost:8081/api/v1/web/login`

- 客户端API: `http://localhost:8080/api/v1/consumer`
  - 登录接口: `http://localhost:8080/api/v1/consumer/login`

## 部署架构

系统支持多种部署方式:

1. **单机部署**: 两个服务部署在同一台服务器
2. **分布式部署**: 
   - 后台管理系统部署在内网
   - 客户端API部署在外网
   - 共享相同的数据库和Redis

## 接口文档

暂无集成的Swagger文档，可通过代码查看API定义：

- 后台接口: `chaperone-web/src/main/java/com/avatar/hospital/chaperone/web/controller/`
- 客户端接口: `chaperone-consumer/src/main/java/com/avatar/hospital/chaperone/consumer/controller/` 
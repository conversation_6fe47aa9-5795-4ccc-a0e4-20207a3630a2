# 计划查询问题修复总结

## 🔍 问题描述

在使用 `PatrolPlanRepositoryService.selectAllWithoutTenant()` 方法时，无法获取到所有的计划数据。

## 🔍 问题原因分析

### 主要问题：参数传递错误

在 `PatrolPlanRepositoryServiceImpl.selectAllWithoutTenant()` 方法中，传入的 `queryWrapper` 参数被忽略了：

```java
// ❌ 错误的实现
@Override
public List<PatrolPlanDO> selectAllWithoutTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper) {
    return baseMapper.selectAllWithoutTenant(queryWrapper()); // 忽略了传入的参数
}
```

**问题分析**：
- 方法接收了一个 `queryWrapper` 参数，但在调用 Mapper 时使用了 `queryWrapper()` 方法
- `queryWrapper()` 方法会创建一个新的查询条件，忽略了调用者传入的查询条件
- 这导致传入的查询条件（如状态过滤等）被完全忽略

### 次要问题：租户隔离配置

虽然使用了 `@InterceptorIgnore(tenantLine = "true")` 注解，但需要确认：
1. 注解是否在正确的位置（Mapper层）
2. `t_project_patrol_plan` 表是否在租户隔离列表中
3. 实体类是否有正确的 `hid` 字段

## ✅ 解决方案

### 1. 修复参数传递问题

```java
// ✅ 正确的实现
@Override
public List<PatrolPlanDO> selectAllWithoutTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper) {
    // 使用传入的queryWrapper参数，而不是自己创建的
    return baseMapper.selectAllWithoutTenant(queryWrapper);
}
```

### 2. 验证租户隔离配置

#### 2.1 Mapper层注解配置 ✅

```java
@Mapper
public interface PatrolPlanMapper extends BaseMapper<PatrolPlanDO> {

    // 忽略多租户插件，查询所有租户的数据
    @InterceptorIgnore(tenantLine = "true")
    default List<PatrolPlanDO> selectAllWithoutTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper) {
        return selectList(queryWrapper);
    }
}
```

#### 2.2 租户表配置 ✅

在 `TenantHandler` 中，`t_project_patrol_plan` 已正确配置：

```java
private static final List<String> TENANT_TABLES = Arrays.asList(
    // 项目相关表
    "t_project_patrol_plan",  // ✅ 已配置
    "t_project_maintenance_plan",
    // ...
);
```

#### 2.3 实体类字段配置 ✅

```java
// PatrolPlanDO 继承链
PatrolPlanDO extends PlanDO extends TenantBaseDO

// TenantBaseDO 包含 hid 字段
public class TenantBaseDO {
    /**
     * 院区ID
     */
    private long hid;  // ✅ 有 hid 字段
}
```

## 🧪 测试验证

### 测试用例

```java
@Test
public void testSelectAllPlan() {
    // 创建查询条件
    LambdaQueryWrapper<PatrolPlanDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PatrolPlanDO::getStatus, PlanStatusType.VALID.getCode());
    
    // 调用修复后的方法
    List<PatrolPlanDO> list = repositoryService.selectAllWithoutTenant(queryWrapper);
    
    // 验证结果
    for (PatrolPlanDO planDO : list) {
        System.out.println(planDO);
        // 应该只返回状态为 VALID 的计划
        assertEquals(PlanStatusType.VALID.getCode(), planDO.getStatus());
    }
}
```

### 预期结果

1. **查询条件生效**：只返回状态为 `VALID` 的计划
2. **忽略租户隔离**：返回所有租户的计划数据
3. **SQL 不包含租户条件**：生成的 SQL 不应包含 `WHERE hid = ?`

## 📊 修复前后对比

### 修复前

```java
// 传入的查询条件被忽略
LambdaQueryWrapper<PatrolPlanDO> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(PatrolPlanDO::getStatus, PlanStatusType.VALID.getCode());

// 实际执行的是默认查询条件（只有 deleted = 1）
List<PatrolPlanDO> list = repositoryService.selectAllWithoutTenant(queryWrapper);
// 结果：返回所有状态的计划，包括作废的
```

### 修复后

```java
// 传入的查询条件被正确使用
LambdaQueryWrapper<PatrolPlanDO> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(PatrolPlanDO::getStatus, PlanStatusType.VALID.getCode());

// 实际执行的是传入的查询条件
List<PatrolPlanDO> list = repositoryService.selectAllWithoutTenant(queryWrapper);
// 结果：只返回状态为 VALID 的计划
```

## 🎯 最佳实践

### 1. 参数使用原则

- **始终使用传入的参数**：不要在方法内部忽略传入的参数
- **参数命名要清晰**：避免与内部方法同名导致混淆
- **添加注释说明**：明确参数的用途和预期行为

### 2. 租户隔离忽略的正确方式

```java
// ✅ 正确：在 Mapper 层使用注解
@InterceptorIgnore(tenantLine = "true")
default List<PatrolPlanDO> selectAllWithoutTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper) {
    return selectList(queryWrapper);
}

// ❌ 错误：在 Service 层使用注解（无效）
@InterceptorIgnore(tenantLine = "true")
public List<PatrolPlanDO> selectAllWithoutTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper) {
    return baseMapper.selectList(queryWrapper);
}
```

### 3. 方法设计建议

```java
// 建议的方法签名和实现
public interface PatrolPlanRepositoryService {
    
    /**
     * 查询所有租户的计划（忽略租户隔离）
     * 
     * @param queryWrapper 查询条件（必须传入，不能为null）
     * @return 计划列表
     */
    List<PatrolPlanDO> selectAllWithoutTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper);
    
    /**
     * 查询当前租户的计划（应用租户隔离）
     * 
     * @param queryWrapper 查询条件
     * @return 计划列表
     */
    List<PatrolPlanDO> selectByCurrentTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper);
}
```

## 📝 总结

### ✅ 已修复的问题

1. **参数传递错误** - 现在正确使用传入的 `queryWrapper` 参数
2. **查询条件被忽略** - 传入的查询条件现在会被正确应用
3. **方法行为不符合预期** - 方法现在按照参数和注解的预期工作

### 🎯 修复效果

- ✅ 查询条件正确生效
- ✅ 租户隔离正确忽略
- ✅ 方法行为符合预期
- ✅ 代码逻辑更加清晰

现在 `selectAllWithoutTenant` 方法应该可以正常工作，能够根据传入的查询条件获取所有租户的计划数据！

package com.avatar.hospital.chaperone.database.plan.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanStatusType;
import com.avatar.hospital.chaperone.database.plan.mapper.PatrolPlanMapper;
import com.avatar.hospital.chaperone.database.plan.repository.PatrolPlanRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 巡检计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class PatrolPlanRepositoryServiceImpl extends ServiceImpl<PatrolPlanMapper, PatrolPlanDO> implements PatrolPlanRepositoryService {


    @Override
    public boolean update(PlanRequest request) {
        LambdaUpdateWrapper<PatrolPlanDO> update = updateWrapper();
        update.eq(PlanDO::getId, request.getId());
        update.set(Objects.nonNull(request.getName()), PlanDO::getName, request.getName());
        update.set(Objects.nonNull(request.getCircleType()), PlanDO::getCircleType, request.getCircleType());
        update.set(Objects.nonNull(request.getCircle()), PlanDO::getCircle, request.getCircle());
        update.set(Objects.nonNull(request.getRemark()), PlanDO::getRemark, request.getRemark());

        update.set(PlanDO::getUpdateBy, request.getOperator());
        update.set(PlanDO::getUpdatedAt, LocalDateTime.now());
        return this.update(update);
    }

    @Override
    public boolean abandon(PlanRequest request) {
        LambdaUpdateWrapper<PatrolPlanDO> update = updateWrapper();
        update.eq(PlanDO::getId, request.getId());

        update.set(PlanDO::getStatus, PlanStatusType.ABANDON.getCode());
        update.set(PlanDO::getUpdateBy, request.getOperator());
        update.set(PlanDO::getUpdatedAt, LocalDateTime.now());
        return this.update(update);
    }

    @Override
    public LambdaQueryWrapper<PatrolPlanDO> queryWrapper() {
        LambdaQueryWrapper<PatrolPlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatrolPlanDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    @Override
    public LambdaUpdateWrapper<PatrolPlanDO> updateWrapper() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PatrolPlanDO.class);

        LambdaUpdateWrapper<PatrolPlanDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PatrolPlanDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }

    @Override
    public List<PatrolPlanDO> selectAllWithoutTenant(LambdaQueryWrapper<PatrolPlanDO> queryWrapper) {
        return baseMapper.selectAllWithoutTenant(queryWrapper);
    }


}

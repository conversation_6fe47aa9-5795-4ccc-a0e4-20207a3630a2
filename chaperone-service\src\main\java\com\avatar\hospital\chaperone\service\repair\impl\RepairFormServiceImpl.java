package com.avatar.hospital.chaperone.service.repair.impl;

import com.avatar.hospital.chaperone.builder.repair.RepairFormBuilder;
import com.avatar.hospital.chaperone.database.baccount.repository.WebAccountRepositoryService;
import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.avatar.hospital.chaperone.database.device.dataobject.ProjectDeviceDO;
import com.avatar.hospital.chaperone.database.device.repository.ProjectDeviceRepositoryService;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormTaskDO;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormStatus;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormRepositoryService;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormTaskRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.repair.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.repair.*;
import com.avatar.hospital.chaperone.response.repair.dto.RepairFormExportDTO;
import com.avatar.hospital.chaperone.service.code.ProjectCodeService;
import com.avatar.hospital.chaperone.service.repair.RepairFormFeedbackService;
import com.avatar.hospital.chaperone.service.repair.RepairFormService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 11:03
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RepairFormServiceImpl implements RepairFormService {
    private final RepairFormRepositoryService repairFormRepositoryService;
    private final RepairFormTaskRepositoryService repairFormTaskRepositoryService;
    private final WebAccountRepositoryService webAccountRepositoryService;
    private final ProjectDeviceRepositoryService projectDeviceRepositoryService;
    private final RepairFormFeedbackService repairFormFeedbackService;
    private final ProjectCodeService projectCodeService;

    @Override
    public PageResponse<RepairFormResponse> paging(RepairFormPageRequest request) {
        LambdaQueryWrapper<RepairFormDO> queryWrapper = queryWrapper();
        queryWrapper.eq(Objects.nonNull(request.getCode()), RepairFormDO::getCode, request.getCode());
        queryWrapper.eq(Objects.nonNull(request.getStatus()), RepairFormDO::getStatus, request.getStatus());
        queryWrapper.eq(Objects.nonNull(request.getBizType()), RepairFormDO::getBizType, request.getBizType());
        queryWrapper.in(!CollectionUtils.isEmpty(request.getBizIds()), RepairFormDO::getBizId, request.getBizIds());

        queryWrapper.orderByDesc(RepairFormDO::getCreatedAt);
        Page<RepairFormDO> page = request.ofPage();
        page = repairFormRepositoryService.page(page, queryWrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageResponse.empty(request.getPageIndexLong(), request.getPageSizeLong());
        }
        // 关联参数加载
        List<Long> formIdList = CollUtils.toListLongDistinct(page.getRecords(), RepairFormDO::getId);
        List<RepairFormTaskDO> taskDOList = repairFormTaskRepositoryService.getByFromIdList(formIdList);
        Map<Long, String> accountIdNameRef = webAccountRepositoryService.findMapByIdList(getLinkBAccountId(page.getRecords(), taskDOList));
        // Map<Long,String> deviceIdNameRef = projectDeviceRepositoryService.findMapByIdList(getLinkDeviceId(page.getRecords()));
        Map<Long, RepairFormFeedbackResponse> lastFeedbackRef = repairFormFeedbackService.getLastRepairFormFeedback(formIdList);
        List<RepairFormResponse> list = RepairFormBuilder.toRepairFormResponseList(page.getRecords(), taskDOList, accountIdNameRef, lastFeedbackRef);
        return PageResponse.build(page, list);
    }

    /**
     * 获取关联用户ID
     *
     * @param list
     * @return
     */
    public List<Long> getLinkBAccountId(List<RepairFormDO> list,
                                        List<RepairFormTaskDO> taskDOList) {
        Set<Long> baccountIdSet = new HashSet<>();
        Set<Long> deviceIdSet = new HashSet<>();
        for (RepairFormDO repairFormDO : list) {
            baccountIdSet.add(repairFormDO.getCreateBy());
            deviceIdSet.add(repairFormDO.getDeviceId());
        }
        if (!CollectionUtils.isEmpty(taskDOList)) {
            for (RepairFormTaskDO taskDO : taskDOList) {
                baccountIdSet.add(taskDO.getAssignerAccountId());
                baccountIdSet.add(taskDO.getExecutorAccountId());
            }
        }
        return baccountIdSet.stream()
                .collect(Collectors.toList());
    }

    /**
     * 获取设备ID
     *
     * @param list
     * @return
     */
    public List<Long> getLinkDeviceId(List<RepairFormDO> list) {
        Set<Long> deviceIdSet = new HashSet<>();
        for (RepairFormDO repairFormDO : list) {
            deviceIdSet.add(repairFormDO.getDeviceId());
        }
        return deviceIdSet.stream()
                .collect(Collectors.toList());
    }

    @Override
    public RepairFormIdResponse create(RepairFormCreateRequest request) {
        ProjectDeviceDO deviceDO = projectDeviceRepositoryService.getById(request.getDeviceId());
        AssertUtils.isNotNull(deviceDO, ErrorCode.PROJECT_DEVICE_NOT_EXIST);

        Long id = IdUtils.getId();
        String code = projectCodeService.generateCode(CodeBizType.BXD);
        RepairFormDO repairForm = RepairFormBuilder.createByPCCreate(request, code, id, deviceDO);
        repairFormRepositoryService.add(repairForm);
        return RepairFormBuilder.buildRepairFormIdResponse(id);
    }

    @Override
    public RepairFormIdResponse modify(RepairFormModifyRequest request) {
        RepairFormDO repairFormDO = repairFormRepositoryService.getById(request.getId());
        AssertUtils.isNotNull(repairFormDO, ErrorCode.REPAIR_FORM_NOT_EXIST);
        ProjectDeviceDO deviceDO = projectDeviceRepositoryService.getById(request.getDeviceId());
        AssertUtils.isNotNull(deviceDO, ErrorCode.PROJECT_DEVICE_NOT_EXIST);

        RepairFormDO newrepairFormDO = RepairFormBuilder.modifyByModify(repairFormDO, request, deviceDO);
        repairFormRepositoryService.updateById(newrepairFormDO);
        return RepairFormBuilder.buildRepairFormIdResponse(request.getId());
    }

    @Override
    public RepairFormIdResponse assignExecutor(RepairFormAssignExecutorRequest request) {
        RepairFormDO repairFormDO = repairFormRepositoryService.getById(request.getId());
        AssertUtils.isNotNull(repairFormDO, ErrorCode.REPAIR_FORM_NOT_EXIST);

        List<RepairFormTaskDO> dbRepairFormTaskDOList = repairFormTaskRepositoryService.getByFromId(request.getId());
        List<RepairFormTaskDO> repairFormTaskDOList = RepairFormBuilder.createAssignExecutor(repairFormDO, dbRepairFormTaskDOList, request);
        RepairFormDO newRepairFormDO = RepairFormBuilder.createModifyAssignerAccountId(repairFormDO, request);
        repairFormRepositoryService.assignExecutor(newRepairFormDO, repairFormTaskDOList);
        return RepairFormBuilder.buildRepairFormIdResponse(request.getId());
    }

    @Override
    public RepairFormIdResponse auth(RepairFormAuthRequest request) {
        RepairFormDO repair = repairFormRepositoryService.getById(request.getId());
        AssertUtils.isNotNull(repair, ErrorCode.REPAIR_FORM_NOT_EXIST);

        RepairFormDO updateEntity = new RepairFormDO();
        updateEntity.setId(repair.getId());
        updateEntity.setUpdatedAt(LocalDateTime.now());
        if (request.isPass()) {
            LocalDateTime now = LocalDateTime.now();
            updateEntity.setStatus(RepairFormStatus.FINISH.getStatus());
            updateEntity.setCompletedTime(now);

            Long sec = DateUtils.getSecond(now, repair.getCreatedAt());
            updateEntity.setProcessTime(sec);
        }
        if (request.isReject()) {
            updateEntity.setStatus(RepairFormStatus.NOT_FINISH.getStatus());
        }

        repairFormRepositoryService.updateById(updateEntity);
        return RepairFormIdResponse.builder()
                .id(repair.getId())
                .build();
    }

    @Override
    public RepairFormIdResponse createForC(RepairFormCreateRequest request) {
        ProjectDeviceDO deviceDO = projectDeviceRepositoryService.getById(request.getDeviceId());
        AssertUtils.isNotNull(deviceDO, ErrorCode.PROJECT_DEVICE_NOT_EXIST);

        String code = projectCodeService.generateCode(CodeBizType.BXD);
        Long id = IdUtils.getId();

        RepairFormDO repairForm = RepairFormBuilder.createByH5Create(request, code, id, deviceDO);
        repairFormRepositoryService.add(repairForm);
        return RepairFormBuilder.buildRepairFormIdResponse(id);
    }

    @Override
    public PageResponse<RepairFormCResponse> pagingForC(RepairFormPageCRequest request) {
        Page<RepairFormDO> page = request.ofPage();
        Long total = repairFormRepositoryService.countForC(page, request);
        if (total <= 0) {
            return PageResponse.build(total, request.getPageIndexLong(), request.getPageSizeLong(), Collections.emptyList());
        }
        List<RepairFormDO> repairFormlist = repairFormRepositoryService.listForC(page, request);
        List<Long> repairIdList = CollUtils.toListLongDistinct(repairFormlist, RepairFormDO::getId);
        List<RepairFormTaskDO> repairFormTaskDOList = repairFormTaskRepositoryService.getByFromIdList(repairIdList);
        // 关联参数加载
        Map<Long, String> accountIdNameRef = webAccountRepositoryService.findMapByIdList(getLinkBAccountId(repairFormlist, repairFormTaskDOList));
        Map<Long, String> deviceIdNameRef = projectDeviceRepositoryService.findMapByIdList(getLinkDeviceId(repairFormlist));
        List<RepairFormCResponse> list = RepairFormBuilder.toRepairFormCResponseList(repairFormlist, repairFormTaskDOList, accountIdNameRef, deviceIdNameRef);
        return PageResponse.build(page, list);
    }

    @Override
    public RepairFormDetailResponse detailForC(RepairFormDetailRequest request) {
        RepairFormDO repairFormDO = repairFormRepositoryService.getById(request.getId());
        AssertUtils.isNotNull(repairFormDO, ErrorCode.REPAIR_FORM_NOT_EXIST);
        RepairFormDetailResponse response = RepairFormBuilder.creatRepairFormDetailResponse(repairFormDO);
        return response;
    }

    @Override
    public List<RepairFormExportDTO> exportPaging(RepairFormExportRequest request) {
        PageResponse<RepairFormResponse> paging = paging(RepairFormBuilder.toRepairFormPageRequest(request));
        return RepairFormBuilder.toRepairFormExportDTOList(paging.getRecords());
    }

    private LambdaQueryWrapper<RepairFormDO> queryWrapper() {
        LambdaQueryWrapper<RepairFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairFormDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}

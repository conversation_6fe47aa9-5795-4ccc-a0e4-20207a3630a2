package com.avatar.hospital.chaperone.database.plan.repository;

import com.avatar.hospital.chaperone.TestApplication;
import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanStatusType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(classes = TestApplication.class)
class PatrolPlanRepositoryServiceTest {


    @Autowired
    private PatrolPlanRepositoryService repositoryService;

    /**
     * 查询所有巡检计划(去除租户隔离)
     */
    @Test
    public void testSelectAllPlan() {
        try {
            System.out.println("开始测试 selectAllWithoutTenant 方法...");

            // 创建查询条件
            LambdaQueryWrapper<PatrolPlanDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PatrolPlanDO::getStatus, PlanStatusType.VALID.getCode());
            queryWrapper.eq(PatrolPlanDO::getDeleted, 0L);

            System.out.println("查询条件创建完成，开始执行查询...");

            // 执行查询
            List<PatrolPlanDO> list = repositoryService.selectAllWithoutTenant(queryWrapper);

            System.out.println("查询完成，结果数量: " + (list != null ? list.size() : "null"));

            if (list != null) {
                for (PatrolPlanDO planDO : list) {
                    System.out.println("计划ID: " + planDO.getId() + ", 名称: " + planDO.getName() + ", 状态: " + planDO.getStatus());
                }
            }

            System.out.println("测试完成！");

        } catch (Exception e) {
            System.err.println("测试执行出错: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

}
package com.avatar.hospital.chaperone.web.controller.part;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.request.part.PartStockApplyAuditRequest;
import com.avatar.hospital.chaperone.request.part.PartStockApplyRequest;
import com.avatar.hospital.chaperone.request.part.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.part.PartStockApplyVO;
import com.avatar.hospital.chaperone.service.part.PartStockApplyService;
import com.avatar.hospital.chaperone.service.part.consts.CacheKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 备件入库申请单
 * @date 2023/10/27 15:46
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/project/part/stock")
public class PartStockApplyController {

    private final PartStockApplyService partStockApplyService;


    /**
     * 创建
     *
     * @param request
     * @return
     */
    @Idempotent(value = CacheKey.PART_STOCK_CREATE_KEY + " + #request.PartBatchs")
    @PostMapping("create")
    public SingleResponse<Long> create(@RequestBody PartStockApplyRequest request) {
        return TemplateProcess.doProcess(log, () -> partStockApplyService.create(request));
    }

    /**
     * 更新
     *
     * @param request
     * @return
     */
    @Idempotent(value = CacheKey.PART_STOCK_UPDATE_KEY + " + #request.id")
    @PostMapping("update")
    public SingleResponse<Boolean> update(@RequestBody PartStockApplyRequest request) {
        return TemplateProcess.doProcess(log, () -> {

            return partStockApplyService.update(request);
        });
    }


    /**
     * 查询
     *
     * @param request
     * @return
     */
    @GetMapping("paging")
    public SingleResponse<PageResponse<PartStockApplyVO>> paging(QueryRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return partStockApplyService.paging(request);
        });
    }

    /**
     * 查询-详情
     *
     * @param id
     * @return
     */
    @GetMapping("detail/{id}")
    public SingleResponse<PartStockApplyVO> getById(@PathVariable("id") Long id) {
        return TemplateProcess.doProcess(log, () -> {
            return partStockApplyService.detail(id);
        });
    }

    /**
     * 审核
     *
     * @param request
     * @return
     */
    @Idempotent(value = CacheKey.PART_UPDATE_KEY + " + #request.id")
    @PostMapping("audit")
    public SingleResponse<Boolean> audit(@RequestBody PartStockApplyAuditRequest request) {
        return TemplateProcess.doProcess(log, () -> partStockApplyService.audit(request));
    }
}

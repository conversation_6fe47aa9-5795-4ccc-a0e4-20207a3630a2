package com.avatar.hospital.chaperone.web.controller.baccount;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.WebAccountAddRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountAllocationRoleRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountPagingRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountUpdatePasswordRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountAddResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountAllocationRoleResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountPagingResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountUpdatePasswordResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountUpdateResponse;
import com.avatar.hospital.chaperone.service.baccount.WebAccountService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import com.avatar.hospital.chaperone.web.validator.baccount.AccountValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: B端用户中心接口
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/account")
public class WebAccountController {

    private final WebAccountService webAccountService;

    /**
     * 添加B端用户
     *
     * @param request -
     * @return -
     */
    @PostMapping(value = "")
    public SingleResponse<WebAccountAddResponse> add(@RequestBody WebAccountAddRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("AccountController add request:{}", JSON.toJSONString(request));
            AccountValidator.addValidate(request);
            return webAccountService.add(request);
        });
    }

    /**
     * 更新B端用户
     *
     * @param request -
     * @return -
     */
    @PutMapping(value = "")
    public SingleResponse<WebAccountUpdateResponse> update(@RequestBody WebAccountUpdateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("AccountController update request:{}", JSON.toJSONString(request));
            AccountValidator.updateValidate(request);
            return webAccountService.update(request);
        });
    }

    /**
     * 修改B端用户密码
     *
     * @param request -
     * @return -
     */
    @PutMapping(value = "/change-password")
    public SingleResponse<WebAccountUpdatePasswordResponse> changePassword(@RequestBody WebAccountUpdatePasswordRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("AccountController changePassword request:{}", JSON.toJSONString(request));
            AccountValidator.changePasswordValidate(request);
            return webAccountService.changePassword(request);
        });
    }

    /**
     * 删除B端用户
     *
     * @param request -
     * @return -
     */
    @PutMapping(value = "/delete")
    public SingleResponse<WebAccountDeleteResponse> delete(@RequestBody WebAccountDeleteRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("AccountController delete request:{}", JSON.toJSONString(request));
            AccountValidator.deleteValidate(request);
            return webAccountService.delete(request);
        });
    }

    /**
     * B端用户分配角色
     *
     * @param request -
     * @return -
     */
    @PostMapping(value = "/allocation-role")
    public SingleResponse<WebAccountAllocationRoleResponse> allocationRole(@RequestBody WebAccountAllocationRoleRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("AccountController allocationRole request:{}", JSON.toJSONString(request));
            AccountValidator.allocationRoleValidate(request);
            return webAccountService.allocationRole(request);
        });
    }

    /**
     * 分页查询B端用户
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/paging")
    public SingleResponse<PageResponse<WebAccountPagingResponse>> paging(WebAccountPagingRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("AccountController paging request:{}", JSON.toJSONString(request));
            AccountValidator.pagingValidate(request);
            return webAccountService.paging(request);
        });
    }

    /**
     * 查询B端用户详情
     *
     * @param accountId -
     * @return -
     */
    @GetMapping(value = "/{id}")
    public SingleResponse<WebAccountDetailResponse> detail(@PathVariable("id") Long accountId) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("AccountController detail request:{}", accountId);
            AssertUtils.isNotNull(accountId, ErrorCode.PARAMETER_ERROR);
            return webAccountService.detail(accountId);
        });
    }

    /**
     * 查询B端用户详情
     *
     * @return -
     */
    @GetMapping(value = "/current-account-detail")
    public SingleResponse<WebAccountDetailResponse> getCurrentAccountDetail() {
        return TemplateProcess.doProcess(log, () -> webAccountService.detail(WebAccountUtils.getCurrentAccountIdAndThrow()));
    }

}

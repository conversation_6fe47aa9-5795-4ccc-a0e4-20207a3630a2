package com.avatar.hospital.chaperone.service.plan.impl;

import com.avatar.hospital.chaperone.builder.plan.PlanBuilder;
import com.avatar.hospital.chaperone.component.baccount.OrganizationComponent;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanDO;
import com.avatar.hospital.chaperone.database.plan.enums.CircleType;
import com.avatar.hospital.chaperone.database.plan.enums.PlanStatusType;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRefDeviceRepositoryAdaptor;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRefExecutorRepositoryAdaptor;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRefOrgRepositoryAdaptor;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRepositoryAdaptor;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.request.plan.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.plan.PlanTaskVO;
import com.avatar.hospital.chaperone.response.plan.PlanVO;
import com.avatar.hospital.chaperone.service.baccount.WebAccountService;
import com.avatar.hospital.chaperone.service.device.DeviceService;
import com.avatar.hospital.chaperone.service.plan.PlanService;
import com.avatar.hospital.chaperone.service.plan.PlanTaskService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.avatar.hospital.chaperone.enums.ErrorCode.PROJECT_PLAN_NOT_EXIST;

/**
 * 计划服务实现类
 * <p>
 * 负责处理巡检计划和维保计划的核心业务逻辑，包括：
 * 1. 计划的创建、更新、查询、详情获取
 * 2. 计划与设备、执行人、部门的关联关系管理
 * 3. 计划的定时执行和任务生成
 * 4. 计划的作废处理
 *
 * <AUTHOR>
 * @description 计划管理核心服务，支持巡检计划和维保计划的全生命周期管理
 * @date 2023/10/30 10:48
 */
@Service("PlanService")
public class PlanServiceImpl implements PlanService {

    // ==================== 数据访问层依赖 ====================

    /**
     * 计划数据访问适配器 - 处理计划主表的CRUD操作
     */
    @Autowired
    PlanRepositoryAdaptor planRepositoryService;

    /**
     * 计划设备关联数据访问适配器 - 处理计划与设备的关联关系
     */
    @Autowired
    PlanRefDeviceRepositoryAdaptor refDeviceRepositoryService;

    /**
     * 计划部门关联数据访问适配器 - 处理计划与部门的关联关系
     */
    @Autowired
    PlanRefOrgRepositoryAdaptor refOrgRepositoryService;

    /**
     * 计划执行人关联数据访问适配器 - 处理计划与执行人的关联关系
     */
    @Autowired
    PlanRefExecutorRepositoryAdaptor refExecutorRepositoryService;

    /**
     * 计划任务服务 - 处理计划任务的生成、执行、过期等操作
     */
    @Autowired
    @Qualifier("PlanTaskService")
    PlanTaskService planTaskService;

    // ==================== 业务组件依赖 ====================

    /**
     * 组织架构组件 - 获取部门信息
     */
    @Autowired
    OrganizationComponent organizationComponent;

    /**
     * 账户服务 - 获取用户信息
     */
    @Autowired
    WebAccountService accountService;

    /**
     * 设备服务 - 获取设备信息
     */
    @Autowired
    DeviceService deviceService;

    /**
     * 创建计划
     * <p>
     * 创建一个新的计划（巡检或维保），包括：
     * 1. 保存计划基本信息
     * 2. 建立计划与设备的关联关系
     * 3. 建立计划与执行人的关联关系
     * 4. 建立计划与部门的关联关系
     *
     * @param request 计划创建请求，包含计划基本信息和关联关系
     * @return 创建成功的计划ID
     * @throws Exception 创建过程中的任何异常都会回滚事务
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long create(PlanRequest request) {
        // 1. 构建计划实体对象
        PlanDO planDO = PlanBuilder.build(request);

        // 2. 保存计划主表数据
        planRepositoryService.save(request.getPlanType(), planDO);

        // 3. 设置计划ID，用于后续关联关系的建立
        request.setId(planDO.getId());

        // 4. 建立计划与设备的关联关系
        if (CollectionUtils.isNotEmpty(request.getDeviceIds())) {
            refDeviceRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefDevice(request));
        }

        // 5. 建立计划与执行人的关联关系
        if (CollectionUtils.isNotEmpty(request.getExecutorIds())) {
            refExecutorRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefExecutor(request));
        }

        // 6. 建立计划与部门的关联关系
        if (CollectionUtils.isNotEmpty(request.getOrgIds())) {
            refOrgRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefOrg(request));
        }

        return planDO.getId();
    }

    /**
     * 更新计划
     * <p>
     * 更新现有计划的信息，包括：
     * 1. 更新计划基本信息
     * 2. 重新建立计划与设备的关联关系（先删除旧关联，再建立新关联）
     * 3. 重新建立计划与执行人的关联关系
     * 4. 重新建立计划与部门的关联关系
     *
     * @param request 计划更新请求，包含要更新的计划信息
     * @return 更新是否成功
     * @throws Exception 更新过程中的任何异常都会回滚事务
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean update(PlanRequest request) {
        // 1. 验证计划是否存在
        PlanDO planDO = planRepositoryService.getById(request.getPlanType(), request.getId());
        AssertUtils.isNotNull(planDO, PROJECT_PLAN_NOT_EXIST);

        // 2. 设置更新时间和操作人
        planDO.setUpdatedAt(LocalDateTime.now());
        planDO.setUpdateBy(request.getOperator());

        // 3. 更新计划主表信息
        planRepositoryService.update(request);

        // 4. 更新设备关联关系：先删除旧关联，再建立新关联
        if (CollectionUtils.isNotEmpty(request.getDeviceIds())) {
            refDeviceRepositoryService.update2delete(request);  // 逻辑删除旧关联
            refDeviceRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefDevice(request));  // 建立新关联
        }

        // 5. 更新执行人关联关系：先删除旧关联，再建立新关联
        if (CollectionUtils.isNotEmpty(request.getExecutorIds())) {
            refExecutorRepositoryService.update2delete(request);  // 逻辑删除旧关联
            refExecutorRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefExecutor(request));  // 建立新关联
        }

        // 6. 更新部门关联关系：先删除旧关联，再建立新关联
        if (Objects.nonNull(request.getOrgIds())) {
            refOrgRepositoryService.update2delete(request);  // 逻辑删除旧关联
            refOrgRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefOrg(request));  // 建立新关联
        }

        return true;
    }

    /**
     * 分页查询计划列表
     * <p>
     * 根据查询条件分页获取计划列表，支持：
     * 1. 按计划编号精确查询
     * 2. 按计划名称模糊查询
     * 3. 按创建时间倒序排列
     * 4. 自动填充关联的设备、执行人、部门信息
     *
     * @param request 查询请求，包含分页参数和查询条件
     * @return 分页结果，包含计划列表和分页信息
     */
    @Override
    public PageResponse<PlanVO> paging(QueryRequest request) {
        // 1. 构建分页对象
        Page<PlanDO> page = request.ofPage();

        // 2. 构建查询条件
        LambdaQueryWrapper<PlanDO> queryWrapper = planRepositoryService.queryWrapper(request.getPlanType());
        queryWrapper.orderByDesc(PlanDO::getId);  // 按ID倒序
        queryWrapper.eq(Objects.nonNull(request.getCode()), PlanDO::getCode, request.getCode());  // 编号精确匹配
        queryWrapper.like(Objects.nonNull(request.getName()), PlanDO::getName, request.getName());  // 名称模糊匹配
        queryWrapper.orderByDesc(PlanDO::getCreatedAt);  // 按创建时间倒序

        // 3. 执行分页查询
        page = planRepositoryService.page(request.getPlanType(), page, queryWrapper);

        // 4. 转换为VO对象
        PageResponse<PlanVO> pageResponse = PageResponse.build(page, PlanBuilder::build);

        // 5. 填充关联信息（设备、执行人、部门名称）
        setName(request.getPlanType(), pageResponse.getRecords());

        return pageResponse;
    }

    /**
     * 为计划列表填充关联信息的名称
     * <p>
     * 批量查询并填充计划关联的设备、执行人、部门的名称信息，
     * 采用批量查询的方式提高性能，避免N+1查询问题。
     *
     * @param planType 计划类型（巡检或维保）
     * @param planVOS  计划VO列表，需要填充关联信息
     */
    private void setName(PlanType planType, List<PlanVO> planVOS) {
        if (CollectionUtils.isEmpty(planVOS)) {
            return;
        }

        // 1. 收集所有计划ID
        Set<Long> planIds = planVOS.stream().map(PlanVO::getId).collect(Collectors.toSet());

        // 2. 批量获取部门关联信息
        Set<Long> orgIds = refOrgRepositoryService.getRefIds(planType, planIds);  // 获取所有关联的部门ID
        Map<Long, String> orgMap = organizationComponent.findOrgMap(orgIds);      // 批量查询部门名称

        // 3. 批量获取执行人关联信息
        Set<Long> accountIds = refExecutorRepositoryService.getRefIds(planType, planIds);  // 获取所有关联的执行人ID
        Map<Long, String> accountMap = accountService.getAccountMap(accountIds);           // 批量查询执行人名称

        // 4. 批量获取设备关联信息
        Set<Long> deviceIds = refDeviceRepositoryService.getRefIds(planType, planIds);  // 获取所有关联的设备ID
        Map<Long, String> deviceMap = deviceService.findDeviceMap(deviceIds);           // 批量查询设备名称

        // 5. 为每个计划填充关联信息
        planVOS.stream().forEach(planVO -> {
            // 填充设备信息：根据当前计划ID获取其关联的设备，并从设备名称映射中提取对应信息
            planVO.setDevices(PlanVO.map2List(getSubMap(deviceMap, refDeviceRepositoryService.getRefIds(planType, planVO.getId()))));
            // 填充执行人信息：根据当前计划ID获取其关联的执行人，并从执行人名称映射中提取对应信息
            planVO.setExecutors(PlanVO.map2List(getSubMap(accountMap, refExecutorRepositoryService.getRefIds(planType, planVO.getId()))));
            // 填充部门信息：根据当前计划ID获取其关联的部门，并从部门名称映射中提取对应信息
            planVO.setDepartments(PlanVO.map2List(getSubMap(orgMap, refOrgRepositoryService.getRefIds(planType, planVO.getId()))));
        });
    }

    /**
     * 从完整映射中提取子映射
     * <p>
     * 根据指定的ID集合，从完整的ID-名称映射中提取对应的子映射。
     * 这是一个工具方法，用于从批量查询的结果中提取特定计划需要的关联信息。
     *
     * @param map 完整的ID-名称映射
     * @param ids 需要提取的ID集合
     * @return 包含指定ID的子映射
     */
    private Map<Long, String> getSubMap(Map<Long, String> map, Collection<Long> ids) {
        // 参数校验：如果ID集合为空或映射为空，返回空映射
        if (CollectionUtils.isEmpty(ids) || Objects.isNull(map)) {
            return Maps.newHashMap();
        }

        // 构建子映射：只包含指定ID的映射关系
        Map<Long, String> subMap = Maps.newHashMap();
        for (Long id : ids) {
            subMap.put(id, map.get(id));
        }
        return subMap;
    }

    /**
     * 获取计划详情
     * <p>
     * 根据计划类型和ID获取单个计划的详细信息，
     * 包括计划基本信息和关联的设备、执行人、部门信息。
     *
     * @param planType 计划类型（巡检或维保）
     * @param id       计划ID
     * @return 计划详情VO，如果计划不存在则返回null
     */
    @Override
    public PlanVO detail(PlanType planType, Long id) {
        // 1. 根据ID查询计划基本信息
        PlanVO planVO = PlanBuilder.build(planRepositoryService.getById(planType, id));
        if (Objects.isNull(planVO)) {
            return null;
        }

        // 2. 填充关联信息（设备、执行人、部门名称）
        setName(planType, Lists.newArrayList(planVO));

        return planVO;
    }

    /**
     * 执行计划定时任务
     * <p>
     * 这是一个定时任务方法，用于处理计划的自动执行逻辑：
     * 1. 查询所有生效的计划（跨租户查询）
     * 2. 检查每个计划是否到了执行周期
     * 3. 将过期未完成的任务标记为已过期
     * 4. 根据计划周期生成新的任务
     * <p>
     * 注意：此方法会处理所有租户的计划，确保系统的计划任务能够正常运行
     *
     * @param planType 计划类型（巡检或维保）
     */
    @Override
    public void execute(PlanType planType) {
        // 1. 获取所有生效的计划（跨租户查询，确保所有租户的计划都能被处理）
        LambdaQueryWrapper<PlanDO> queryWrapper = planRepositoryService.queryWrapper(planType);
        queryWrapper.eq(PlanDO::getStatus, PlanStatusType.VALID.getCode());  // 只处理生效状态的计划
        List<PlanDO> PlanDOS = planRepositoryService.getListWithoutTenant(planType, queryWrapper);

        // 2. 循环处理每个计划
        for (PlanDO planDO : PlanDOS) {
            // 2.1 获取该计划在今日之前的所有任务
            List<PlanTaskVO> taskVOS = planTaskService.getPlanBeforeTodayTask(planType, planDO.getId());

            // 2.2 检查计划是否到了执行周期（是否需要生成新任务）
            if (!checkIsExpired(taskVOS, planDO)) {
                continue;  // 未到执行周期，跳过当前计划
            }

            // 2.3 处理过期任务：将未完成的任务标记为已过期
            expiringTask(planType, taskVOS);

            // 2.4 生成新任务：根据计划的执行周期生成新的任务
            createTask(planType, planDO);
        }
    }

    /**
     * 检查计划是否到了执行周期
     * <p>
     * 根据计划的创建时间、执行周期类型和周期间隔，判断是否需要生成新的任务。
     * 检查逻辑：
     * 1. 计划必须至少创建一天以上
     * 2. 如果没有历史任务，则需要生成任务
     * 3. 如果有历史任务，则根据周期类型计算下次执行时间
     *
     * @param taskVOS 计划的历史任务列表
     * @param planDO  计划信息
     * @return true-需要生成新任务，false-还未到执行周期
     */
    private boolean checkIsExpired(List<PlanTaskVO> taskVOS, PlanDO planDO) {
        LocalDateTime planCreateTime = planDO.getCreatedAt();

        // 1. 判断计划是否已创建超过一天（确保计划不是今天刚创建的）
        boolean isSecondDay = LocalDate.now().isAfter(planCreateTime.toLocalDate());
        if (!isSecondDay) {
            return false;  // 计划今天刚创建，不需要生成任务
        }

        // 2. 如果没有历史任务，说明需要生成第一个任务
        if (CollectionUtils.isEmpty(taskVOS)) {
            return true;
        }

        // 3. 有历史任务时，根据执行周期计算下次执行时间
        LocalDate taskCreatedAt = taskVOS.get(0).getCreatedAt().toLocalDate();  // 最近一次任务的创建时间
        LocalDate taskDueTime = LocalDate.now();  // 下次应该执行的时间

        // 根据周期类型计算下次执行时间
        switch (CircleType.of(planDO.getCircleType())) {
            case DAY:
                // 按天执行：在最近任务创建时间基础上加上指定天数
                taskDueTime = taskCreatedAt.plusDays(planDO.getCircle());
                break;
            case MONTH:
                // 按月执行：在最近任务创建时间基础上加上指定月数
                taskDueTime = taskCreatedAt.plusMonths(planDO.getCircle());
                break;
        }

        // 4. 判断今天是否正好是应该执行的日期
        boolean isDue = LocalDate.now().compareTo(taskDueTime) == 0;

        return isDue;
    }

    /**
     * 为计划创建新任务
     * <p>
     * 根据计划信息和关联的设备，为计划生成新的执行任务。
     * 每个关联的设备都会生成一个对应的任务。
     *
     * @param planType 计划类型（巡检或维保）
     * @param planDO   计划信息
     */
    private void createTask(PlanType planType, PlanDO planDO) {
        // 1. 获取计划关联的所有设备ID
        List<Long> deviceIds = refDeviceRepositoryService.getRefIds(planType, planDO.getId());

        // 2. 为每个设备创建对应的任务
        planTaskService.create(planType, planDO, deviceIds);
    }

    /**
     * 将任务标记为过期
     * <p>
     * 将指定的任务列表中所有未完成的任务标记为已过期状态。
     * 这通常在生成新任务之前执行，确保旧的未完成任务不会影响新任务。
     *
     * @param planType 计划类型（巡检或维保）
     * @param taskVOS  需要标记为过期的任务列表
     */
    private void expiringTask(PlanType planType, List<PlanTaskVO> taskVOS) {
        if (CollectionUtils.isEmpty(taskVOS)) {
            return;  // 没有任务需要处理
        }

        // 1. 提取所有任务ID
        List<Long> taskIds = taskVOS.stream().map(PlanTaskVO::getId).collect(Collectors.toList());

        // 2. 批量将任务标记为过期
        planTaskService.expired(planType, taskIds);
    }

    /**
     * 作废计划
     * <p>
     * 将指定的计划标记为作废状态，同时删除该计划下的所有任务。
     * 作废后的计划将不再参与定时任务的执行。
     *
     * @param request 作废请求，包含计划ID和操作人信息
     * @return 作废是否成功
     * @throws Exception 作废过程中的任何异常都会回滚事务
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean abandon(PlanRequest request) {
        // 1. 验证计划是否存在
        PlanDO planDO = planRepositoryService.getById(request.getPlanType(), request.getId());
        AssertUtils.isNotNull(planDO, PROJECT_PLAN_NOT_EXIST);

        // 2. 将计划标记为作废状态
        planRepositoryService.abandon(request);

        // 3. 删除计划下的所有任务（逻辑删除）
        planTaskService.update2delete(request);

        return true;
    }
}

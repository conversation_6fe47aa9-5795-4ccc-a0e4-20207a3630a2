-- 超级管理员模块数据库初始化脚本

-- 创建超级管理员用户表
CREATE TABLE IF NOT EXISTS `t_admin_user` (
    `id` BIGINT NOT NULL COMMENT '主键ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(100) NOT NULL COMMENT '密码（加密）',
    `real_name` VARCHAR(50) NOT NULL COMMENT '真实姓名',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `avatar` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `role` VARCHAR(20) NOT NULL DEFAULT 'SYSTEM_ADMIN' COMMENT '角色：SUPER_ADMIN-超级管理员，SYSTEM_ADMIN-系统管理员',
    `permission_scope` VARCHAR(20) NOT NULL DEFAULT 'LIMITED' COMMENT '权限范围：ALL-全部权限，LIMITED-受限权限',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
    `create_by` BIGINT DEFAULT NULL COMMENT '创建人',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` BIGINT DEFAULT NULL COMMENT '更新人',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_status` (`status`),
    KEY `idx_role` (`role`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='超级管理员用户表';

-- 插入默认超级管理员账号
-- 用户名: admin, 密码: admin123 (MD5加密后: 21232f297a57a5a743894a0e4a801fc3 + ADMIN_SALT_2024)
INSERT INTO `t_admin_user` (`id`, `username`, `password`, `real_name`, `phone`, `email`, `status`, `role`, `permission_scope`, `remark`, `created_at`, `updated_at`) 
VALUES 
(1, 'admin', 'f25a2fc72690b780b2a14e140ef6a9e0', '超级管理员', '13800138000', '<EMAIL>', 1, 'SUPER_ADMIN', 'ALL', '系统默认超级管理员账号', NOW(), NOW()),
(2, 'system', 'f25a2fc72690b780b2a14e140ef6a9e0', '系统管理员', '13800138001', '<EMAIL>', 1, 'SYSTEM_ADMIN', 'LIMITED', '系统管理员账号', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`username` = VALUES(`username`);

-- 创建管理员操作日志表
CREATE TABLE IF NOT EXISTS `t_admin_operation_log` (
    `id` BIGINT NOT NULL COMMENT '主键ID',
    `admin_user_id` BIGINT NOT NULL COMMENT '管理员用户ID',
    `admin_username` VARCHAR(50) NOT NULL COMMENT '管理员用户名',
    `operation_type` VARCHAR(50) NOT NULL COMMENT '操作类型',
    `operation_desc` VARCHAR(200) NOT NULL COMMENT '操作描述',
    `target_type` VARCHAR(50) DEFAULT NULL COMMENT '操作目标类型',
    `target_id` VARCHAR(100) DEFAULT NULL COMMENT '操作目标ID',
    `tenant_id` BIGINT DEFAULT NULL COMMENT '涉及的租户ID',
    `request_method` VARCHAR(10) DEFAULT NULL COMMENT '请求方法',
    `request_url` VARCHAR(500) DEFAULT NULL COMMENT '请求URL',
    `request_params` TEXT DEFAULT NULL COMMENT '请求参数',
    `response_result` TEXT DEFAULT NULL COMMENT '响应结果',
    `client_ip` VARCHAR(50) DEFAULT NULL COMMENT '客户端IP',
    `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    `execution_time` INT DEFAULT NULL COMMENT '执行时间（毫秒）',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-失败，1-成功',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_admin_user_id` (`admin_user_id`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员操作日志表';

-- 创建租户管理视图（用于统计）
CREATE OR REPLACE VIEW `v_tenant_statistics` AS
SELECT 
    h.id AS tenant_id,
    h.name AS tenant_name,
    h.code AS tenant_code,
    h.status AS tenant_status,
    h.created_at AS tenant_created_at,
    
    -- 用户统计
    COALESCE(b_stats.b_user_count, 0) AS b_user_count,
    COALESCE(c_stats.c_user_count, 0) AS c_user_count,
    COALESCE(b_stats.b_user_count, 0) + COALESCE(c_stats.c_user_count, 0) AS total_user_count,
    
    -- 计划统计
    COALESCE(plan_stats.patrol_plan_count, 0) AS patrol_plan_count,
    COALESCE(plan_stats.maintenance_plan_count, 0) AS maintenance_plan_count,
    COALESCE(plan_stats.total_plan_count, 0) AS total_plan_count,
    
    -- 设备统计
    COALESCE(device_stats.device_count, 0) AS device_count,
    
    -- 订单统计
    COALESCE(order_stats.order_count, 0) AS order_count,
    COALESCE(order_stats.total_amount, 0) AS total_order_amount

FROM (
    -- 这里需要根据实际的医院/院区表结构调整
    SELECT 1 as id, '默认院区' as name, 'DEFAULT' as code, 1 as status, NOW() as created_at
    -- 如果有实际的院区表，替换为: SELECT id, name, code, status, created_at FROM t_hospital
) h

LEFT JOIN (
    SELECT 
        hid,
        COUNT(*) AS b_user_count
    FROM t_b_account 
    WHERE deleted = 0
    GROUP BY hid
) b_stats ON h.id = b_stats.hid

LEFT JOIN (
    SELECT 
        hid,
        COUNT(*) AS c_user_count
    FROM t_c_account 
    WHERE deleted = 0
    GROUP BY hid
) c_stats ON h.id = c_stats.hid

LEFT JOIN (
    SELECT 
        hid,
        SUM(CASE WHEN table_name = 'patrol' THEN plan_count ELSE 0 END) AS patrol_plan_count,
        SUM(CASE WHEN table_name = 'maintenance' THEN plan_count ELSE 0 END) AS maintenance_plan_count,
        SUM(plan_count) AS total_plan_count
    FROM (
        SELECT hid, 'patrol' as table_name, COUNT(*) as plan_count
        FROM t_project_patrol_plan 
        WHERE deleted = 0
        GROUP BY hid
        
        UNION ALL
        
        SELECT hid, 'maintenance' as table_name, COUNT(*) as plan_count
        FROM t_project_maintenance_plan 
        WHERE deleted = 0
        GROUP BY hid
    ) plan_union
    GROUP BY hid
) plan_stats ON h.id = plan_stats.hid

LEFT JOIN (
    SELECT 
        hid,
        COUNT(*) AS device_count
    FROM t_project_device 
    WHERE deleted = 0
    GROUP BY hid
) device_stats ON h.id = device_stats.hid

LEFT JOIN (
    SELECT 
        hid,
        COUNT(*) AS order_count,
        SUM(total_price) AS total_amount
    FROM t_order 
    WHERE deleted = 0
    GROUP BY hid
) order_stats ON h.id = order_stats.hid;

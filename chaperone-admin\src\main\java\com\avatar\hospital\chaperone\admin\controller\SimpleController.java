package com.avatar.hospital.chaperone.admin.controller;

import com.avatar.hospital.chaperone.admin.common.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 简化控制器 - 用于测试基础功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api")
public class SimpleController {

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public Result<String> test() {
        return Result.success("Admin模块基础功能正常！");
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("application", "chaperone-admin");
        info.put("version", "1.0.0-SNAPSHOT");
        info.put("timestamp", LocalDateTime.now());
        info.put("status", "running");
        
        return Result.success("获取系统信息成功", info);
    }

}

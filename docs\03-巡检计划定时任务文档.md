# 医院陪护系统定时任务详细文档

## 概述

本文档详细介绍了医院陪护系统中定时任务的完整实现机制，包括任务调度、执行原理、管理方式、启停控制以及如何添加新的定时任务。系统采用Spring Boot的@Scheduled注解结合自定义任务管理框架，实现了分布式环境下的可靠任务调度。

## 定时任务整体架构

### 1. 系统架构图

```
定时任务系统架构
├── 启动入口层
│   ├── HospitalChaperoneWebApplication (@EnableScheduling)
│   └── 定时任务启用配置
├── 任务调度层
│   ├── PatrolPlanScheduling (巡检计划调度器)
│   ├── MaintenancePlanScheduling (维保计划调度器)
│   └── 其他业务调度器...
├── 任务执行层
│   ├── ScheduledHelper (定时任务执行助手)
│   ├── ThreadPoolConfig (线程池配置)
│   └── 分布式锁管理 (Redisson)
├── 任务管理层
│   ├── JobTaskRegister (任务注册器)
│   ├── ScheduledType (任务类型枚举)
│   └── @Task注解 (任务标识)
├── 日志监控层
│   ├── ScheduledLogRepositoryService (执行日志服务)
│   └── 任务执行状态跟踪
└── 业务逻辑层
    ├── PlanService (计划服务)
    └── 其他业务服务...
```

## 定时任务启动原理

### 2. 定时任务启动流程

#### 2.1 系统启动入口

**主启动类**: `chaperone-web-start/src/main/java/com/avatar/hospital/chaperone/web/HospitalChaperoneWebApplication.java`

```java
@ComponentScan("com.avatar.hospital.chaperone")
@MapperScans(value = {
    @MapperScan(value = "com.avatar.hospital.chaperone.database.baccount.mapper"),
    @MapperScan(value = "com.avatar.hospital.chaperone.database.caccount.mapper"),
    // ... 其他mapper扫描
})
@SpringBootApplication
@EnableScheduling  // 关键注解：启用定时任务
public class HospitalChaperoneWebApplication {
    public static void main(String[] args) {
        SpringApplication.run(HospitalChaperoneWebApplication.class, args);
    }
}
```

**启动流程说明**:
1. **@EnableScheduling注解**: 这是定时任务的总开关，启用Spring的任务调度功能
2. **@ComponentScan**: 扫描所有定时任务类（标注了@Component的调度器）
3. **Spring容器启动**: 自动发现并注册所有@Scheduled方法
4. **任务注册**: JobTaskRegister自动扫描@Task注解的方法并注册

#### 2.2 定时任务自动发现机制

系统启动时，Spring会自动执行以下步骤：

```java
// 1. 扫描所有@Component类
@Component
public class PatrolPlanScheduling { ... }

// 2. 发现@Scheduled方法
@Scheduled(cron = "0 0 1 * * ?")
public void planTask() { ... }

// 3. JobTaskRegister扫描@Task注解
@Task(value = "patrol-plan-task", description = "巡检计划-任务生成")
public Boolean execute(Integer date, Integer hour) { ... }
```

## 核心定时任务类详解

### 3. 主要定时任务调度器

#### 3.1 PatrolPlanScheduling (巡检计划调度器)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/job/plan/PatrolPlanScheduling.java`

```java
@Task(value = "PatrolPlanScheduling", description = "巡检计划任务描述")
@Slf4j
@Component
@RequiredArgsConstructor
public class PatrolPlanScheduling {
    @Autowired
    private ScheduledHelper scheduledHelper;
    @Autowired
    private PlanService planService;

    /**
     * 巡检计划任务 - 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void planTask() {
        Integer hour = DateUtils.hour();
        Integer date = DateUtils.dateInt();
        scheduledHelper.exec(ScheduledType.PATROL_PLAN_TASK_DAY, () -> execute(date, hour));
    }

    @Task(value = "patrol-plan-task", description = "巡检计划-任务生成")
    public Boolean execute(Integer date, Integer hour) {
        planService.execute(PlanType.PATROL);
        return true;
    }
}
```

#### 3.2 MaintenancePlanScheduling (维保计划调度器)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/job/plan/MaintenancePlanScheduling.java`

```java
@Task(value = "MaintenancePlanScheduling", description = "维保计划任务描述")
@Slf4j
@Component
public class MaintenancePlanScheduling {
    @Autowired
    private ScheduledHelper scheduledHelper;
    @Autowired
    private PlanService planService;

    /**
     * 维保计划任务 - 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void planTask() {
        Integer hour = DateUtils.hour();
        Integer date = DateUtils.dateInt();
        scheduledHelper.exec(ScheduledType.MAINTENANCE_PLAN_TASK_DAY, () -> execute(date, hour));
    }

    @Task(value = "maintenance-plan-task", description = "维保计划-任务生成")
    public Boolean execute(Integer date, Integer hour) {
        planService.execute(PlanType.MAINTENANCE);
        return true;
    }
}
```



### 4. ScheduledHelper (定时任务执行助手)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/job/ScheduledHelper.java`

这是定时任务执行的核心组件，提供统一的任务执行框架：

```java
@Slf4j
@Component
@RequiredArgsConstructor
public class ScheduledHelper {
    private final RedissonClient redissonClient;
    @Resource(name = "scheduledHelperExecutor")
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final ScheduledLogRepositoryService scheduledLogRepositoryService;

    @TraceLog
    public <T> void exec(ScheduledType scheduledType, Supplier<T> supplier) {
        log.info("ScheduledHelper 定时任务 start {}", scheduledType.toString());

        // 1. 创建执行日志
        ScheduledLogDO logDO = scheduledLogRepositoryService.create(scheduledType);
        long start = System.currentTimeMillis();

        // 2. 获取分布式锁
        RLock lock = redissonClient.getLock(scheduledType.getLockKey());

        try {
            // 3. 尝试获取锁（最多等待3分钟）
            if (!lock.tryLock(3, TimeUnit.MINUTES)) {
                log.info("ScheduledHelper 定时任务获取锁失败 {}", scheduledType.toString());
                return;
            }

            // 4. 异步执行任务
            threadPoolTaskExecutor.submit(() -> supplier.get());

            // 5. 记录成功日志
            long end = System.currentTimeMillis();
            long cost = end - start;
            log.info("ScheduledHelper 定时任务执行成功 cost:{} ms {}", cost, scheduledType.toString());

            ScheduledLogDO updateEntity = logDO.successByLock(cost);
            scheduledLogRepositoryService.updateById(updateEntity);

        } catch (Exception e) {
            // 6. 记录失败日志
            long end = System.currentTimeMillis();
            long cost = end - start;
            log.error("ScheduledHelper 定时任务执行失败 cost:{}ms {}", cost, scheduledType.toString(), e);

            ScheduledLogDO updateEntity = logDO.failByException(cost);
            scheduledLogRepositoryService.updateById(updateEntity);

        } finally {
            // 7. 释放分布式锁
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }
}
```

**核心特性**:
- **分布式锁**: 使用Redisson防止多实例重复执行
- **异步执行**: 使用专用线程池异步执行任务
- **完整日志**: 记录任务执行的完整生命周期
- **异常处理**: 完善的异常捕获和处理机制
- **性能监控**: 记录任务执行耗时

### 5. ScheduledType (定时任务类型枚举)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/job/ScheduledType.java`

这个枚举定义了系统中所有定时任务的类型和配置：

```java
@Getter
public enum ScheduledType {
    // 计划任务相关
    PATROL_PLAN_TASK_DAY("PATROL_PLAN_TASK_DAY",
                        "巡检计划任务创建(每天执行一次)",
                        "scheduled:patrol_plan_task_day"),
    MAINTENANCE_PLAN_TASK_DAY("MAINTENANCE_PLAN_TASK_DAY",
                             "维保计划任务创建(每天执行一次)",
                             "scheduled:maintenance_plan_task_day"),

    // 订单任务相关
    CONSUMER_LOG_BY_EVERY_HOUR("CONSUMER_LOG_BY_EVERY_HOUR",
                              "消费记录创建(每个小时执行一次)",
                              "scheduled:consumer_log_by_every_hour"),
    SETTLE_BILL_BY_EVERY_HOUR("SETTLE_BILL_BY_EVERY_HOUR",
                             "结算账单创建(每个小时执行一次)",
                             "scheduled:settle_bill_by_every_hour"),
    EXPIRE_CANCEL_BY_EVERY_HOUR("EXPIRE_CANCEL_BY_EVERY_HOUR",
                               "过期自动取消(每个小时执行一次)",
                               "scheduled:expire_cancel_by_every_hour"),

    // 账单任务相关
    ORDER_BILL_BY_EVERY_MONTH("ORDER_BILL_BY_EVERY_MONTH",
                             "周期账单(每个月1号凌晨执行一次)",
                             "scheduled:order_bill_by_every_month"),
    ORDER_BILL_BY_EVERY_SEASON("ORDER_BILL_BY_EVERY_SEASON",
                              "周期账单(每个季度开始1号凌晨执行一次)",
                              "scheduled:order_bill_by_every_season"),
    ORDER_BILL_BY_EVERY_YEAR("ORDER_BILL_BY_EVERY_YEAR",
                            "周期账单(每年1月1号凌晨执行一次)",
                            "scheduled:order_bill_by_every_year"),

    // 护工排班相关
    ORDER_NURSING_BY_EVERY_MONTH("ORDER_NURSING_BY_EVERY_MONTH",
                                "订单护工排班(每年1月27号凌晨执行一次)",
                                "scheduled:order_nursing_by_every_month"),

    // 支付查询相关
    ORDER_PAY_BY_EVERY_MINUTES("ORDER_PAY_BY_EVERY_MINUTES",
                              "支付单支付情况,每分钟30秒执行一次",
                              "scheduled:order_pay_by_every_minutes"),
    ORDER_PAY_MESSAGE_BY_EVERY_DAY("ORDER_PAY_MESSAGE_BY_EVERY_DAY",
                                  "订单逾期提醒每天下午15点提醒一次",
                                  "scheduled:order_pay_message_by_every_day"),
    ORDER_PAY_USER_SYNC_EVERY_DAY("ORDER_PAY_USER_SYNC_EVERY_DAY",
                                 "微信公众号用户同步每天3,13点同步一次",
                                 "scheduled:order_pay_user_sync_by_every_day");

    private final String code;        // 任务代码
    private final String describe;    // 任务描述
    private final String lockKey;     // Redis分布式锁键名

    ScheduledType(String code, String describe, String lockKey) {
        this.code = code;
        this.describe = describe;
        this.lockKey = lockKey;
    }

    @Override
    public String toString() {
        return describe + "(" + code + ")" + ",lockKey:" + lockKey;
    }
}
```

**字段说明**:
- **code**: 任务唯一标识符
- **describe**: 任务的中文描述
- **lockKey**: Redis分布式锁的键名，确保多实例环境下任务不重复执行

### 6. JobTaskRegister (任务注册器)

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/job/JobTaskRegister.java`

这是一个重要的组件，用于自动发现和注册所有标注了@Task注解的方法，并提供手动触发功能：

```java
@Slf4j
@Component
public class JobTaskRegister implements BeanPostProcessor {

    private final Map<String, JobTaskRegisterMeta> register = new HashMap<>();

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<?> clazz = bean.getClass();
        boolean present = clazz.isAnnotationPresent(Task.class);

        if (present) {
            Method[] methods = clazz.getMethods();
            for (Method method : methods) {
                boolean methodTaskPresent = method.isAnnotationPresent(Task.class);
                if (!methodTaskPresent) {
                    continue;
                }

                // 解析方法信息
                Type[] parameterTypes = method.getGenericParameterTypes();
                Task taskAnnotation = method.getAnnotation(Task.class);

                // 创建任务元数据
                JobTaskRegisterMeta meta = new JobTaskRegisterMeta();
                meta.setCode(taskAnnotation.value());
                meta.setDescription(taskAnnotation.description());
                meta.setBean(bean);
                meta.setMethod(method);
                meta.setParameterTypes(parameterTypes);

                // 注册任务
                register.put(meta.getCode(), meta);
                log.info("JobTaskRegister 注册任务: {}", meta.toStr());
            }
        }
        return bean;
    }

    /**
     * 手动调用任务
     * @param code 任务代码
     * @param args 参数数组
     */
    public Boolean invoke(String code, Object[] args) {
        JobTaskRegisterMeta meta = register.get(code);
        if (meta == null) {
            log.error("未找到任务: {}", code);
            return Boolean.FALSE;
        }

        try {
            // 参数类型转换
            Object[] realArgs = new Object[args.length];
            for (int i = 0; i < args.length; i++) {
                realArgs[i] = convertParameter(meta.getParameterTypes()[i], args[i]);
            }

            // 反射调用方法
            meta.getMethod().invoke(meta.getBean(), realArgs);
            log.info("手动触发任务成功: {}", code);
            return Boolean.TRUE;

        } catch (Exception e) {
            log.error("手动触发任务失败: {}", code, e);
            return Boolean.FALSE;
        }
    }

    /**
     * 获取所有已注册的任务
     */
    public Map<String, String> all() {
        Map<String, String> map = new HashMap<>();
        register.forEach((code, meta) -> map.put(code, meta.toStr()));
        return map;
    }
}
```

## 定时任务管理和控制

### 7. 如何关闭定时任务

#### 7.1 全局关闭定时任务

**方法1: 移除@EnableScheduling注解**

在主启动类中注释或删除@EnableScheduling注解：

```java
@SpringBootApplication
// @EnableScheduling  // 注释这行可以全局关闭定时任务
public class HospitalChaperoneWebApplication {
    public static void main(String[] args) {
        SpringApplication.run(HospitalChaperoneWebApplication.class, args);
    }
}
```

**方法2: 通过配置文件控制**

可以在application.yaml中添加配置：

```yaml
spring:
  task:
    scheduling:
      enabled: false  # 关闭定时任务
```

#### 7.2 单个定时任务关闭

**方法1: 注释@Scheduled注解**

```java
@Component
public class PatrolPlanScheduling {

    // @Scheduled(cron = "0 0 1 * * ?")  // 注释这行关闭该定时任务
    public void planTask() {
        // 任务逻辑
    }
}
```

**方法2: 使用条件注解**

```java
@Component
public class PatrolPlanScheduling {

    @Value("${scheduling.patrol.enabled:true}")
    private boolean patrolEnabled;

    @Scheduled(cron = "0 0 1 * * ?")
    public void planTask() {
        if (!patrolEnabled) {
            log.info("巡检计划任务已禁用");
            return;
        }
        // 执行任务逻辑
    }
}
```

然后在配置文件中控制：

```yaml
scheduling:
  patrol:
    enabled: false  # 关闭巡检计划任务
```

#### 7.3 通过环境变量控制

可以通过环境变量动态控制定时任务：

```bash
# 关闭定时任务
export SPRING_TASK_SCHEDULING_ENABLED=false

# 或者在启动时指定
java -jar app.jar --spring.task.scheduling.enabled=false
```

### 8. 手动触发定时任务

#### 8.1 通过HTTP接口触发

系统提供了手动触发定时任务的HTTP接口：

**接口地址**: `GET /api/v1/web/order/cash/compensate/job/invoke`

**请求参数**:
- `sign`: 安全签名（固定值：`sdafas0z2343 0daf`）
- `code`: 任务代码（如：`patrol-plan-task`）
- `objects`: 任务参数数组（可选）

**示例请求**:
```bash
curl -X GET "http://localhost:8081/api/v1/web/order/cash/compensate/job/invoke?sign=sdafas0z2343%200daf&code=patrol-plan-task&objects=20231201,10"
```

**可触发的任务代码**:
- `patrol-plan-task`: 巡检计划任务生成
- `maintenance-plan-task`: 维保计划任务生成
- 等等...

#### 8.2 通过代码直接调用

可以在代码中直接调用JobTaskRegister来触发任务：

```java
@Autowired
private JobTaskRegister jobTaskRegister;

public void manualTriggerTask() {
    // 触发巡检计划任务
    Object[] args = {20231201, 10}; // 日期和小时参数
    Boolean result = jobTaskRegister.invoke("patrol-plan-task", args);

    if (result) {
        log.info("手动触发任务成功");
    } else {
        log.error("手动触发任务失败");
    }
}
```

#### 8.3 查看所有可触发的任务

```java
@Autowired
private JobTaskRegister jobTaskRegister;

public void listAllTasks() {
    Map<String, String> allTasks = jobTaskRegister.all();
    allTasks.forEach((code, description) -> {
        log.info("任务代码: {}, 描述: {}", code, description);
    });
}
```

## 如何添加新的定时任务

### 9. 添加定时任务的完整步骤

#### 9.1 第一步：创建定时任务调度器类

在`chaperone-service/src/main/java/com/avatar/hospital/chaperone/job/`目录下创建新的调度器类：

```java
package com.avatar.hospital.chaperone.job.example;

import com.avatar.hospital.chaperone.annotation.Task;
import com.avatar.hospital.chaperone.job.ScheduledHelper;
import com.avatar.hospital.chaperone.job.ScheduledType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 示例定时任务调度器
 */
@Task(value = "ExampleScheduling", description = "示例定时任务描述")
@Slf4j
@Component
@RequiredArgsConstructor
public class ExampleScheduling {

    @Autowired
    private ScheduledHelper scheduledHelper;

    @Autowired
    private ExampleService exampleService; // 你的业务服务

    /**
     * 示例定时任务 - 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void exampleTask() {
        log.info("开始执行示例定时任务");
        scheduledHelper.exec(ScheduledType.EXAMPLE_TASK_DAY, () -> execute());
    }

    /**
     * 具体的任务执行逻辑
     */
    @Task(value = "example-task", description = "示例任务-具体执行逻辑")
    public Boolean execute() {
        try {
            // 调用业务服务执行具体逻辑
            exampleService.doSomething();
            log.info("示例定时任务执行成功");
            return true;
        } catch (Exception e) {
            log.error("示例定时任务执行失败", e);
            return false;
        }
    }
}
```

#### 9.2 第二步：在ScheduledType枚举中添加新任务类型

在`ScheduledType.java`中添加新的枚举值：

```java
@Getter
public enum ScheduledType {
    // 现有的任务类型...
    PATROL_PLAN_TASK_DAY("PATROL_PLAN_TASK_DAY", "巡检计划任务创建(每天执行一次)", "scheduled:patrol_plan_task_day"),

    // 添加新的任务类型
    EXAMPLE_TASK_DAY("EXAMPLE_TASK_DAY", "示例任务(每天执行一次)", "scheduled:example_task_day"),

    // 其他任务类型...
}
```

#### 9.3 第三步：创建业务服务类

创建具体的业务逻辑服务：

```java
package com.avatar.hospital.chaperone.service.example;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 示例业务服务
 */
@Slf4j
@Service
public class ExampleService {

    public void doSomething() {
        log.info("执行示例业务逻辑");

        // 这里编写你的具体业务逻辑
        // 例如：数据清理、报表生成、消息推送等

        try {
            // 模拟业务处理
            Thread.sleep(1000);
            log.info("示例业务逻辑执行完成");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("业务执行被中断", e);
        }
    }
}
```

#### 9.4 第四步：配置Cron表达式

常用的Cron表达式示例：

```java
// 每分钟执行
@Scheduled(cron = "0 * * * * ?")

// 每小时执行
@Scheduled(cron = "0 0 * * * ?")

// 每天凌晨1点执行
@Scheduled(cron = "0 0 1 * * ?")

// 每天上午9点执行
@Scheduled(cron = "0 0 9 * * ?")

// 每周一凌晨2点执行
@Scheduled(cron = "0 0 2 ? * MON")

// 每月1号凌晨3点执行
@Scheduled(cron = "0 0 3 1 * ?")

// 每个工作日上午10点执行
@Scheduled(cron = "0 0 10 ? * MON-FRI")
```

**Cron表达式格式**: `秒 分 时 日 月 周`

- 秒：0-59
- 分：0-59
- 时：0-23
- 日：1-31
- 月：1-12
- 周：1-7 (1=周日) 或 SUN-SAT

**特殊字符**:
- `*`: 匹配任意值
- `?`: 不指定值（用于日和周字段）
- `-`: 范围（如1-5）
- `,`: 列举（如1,3,5）
- `/`: 步长（如0/15表示每15分钟）

#### 9.5 第五步：编写单元测试

为新的定时任务编写测试：

```java
package com.avatar.hospital.chaperone.job.example;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class ExampleSchedulingTest {

    @Autowired
    private ExampleScheduling exampleScheduling;

    @Test
    public void testExecute() {
        // 测试任务执行
        Boolean result = exampleScheduling.execute();
        assert result != null && result;
    }
}
```

#### 9.6 第六步：验证任务注册

启动应用后，查看日志确认任务已注册：

```
JobTaskRegister 注册任务: 示例任务-具体执行逻辑(example-task)
```

## 定时任务配置详解

### 10. 线程池配置

**文件位置**: `chaperone-service/src/main/java/com/avatar/hospital/chaperone/job/ThreadPoolConfig.java`

```java
@Configuration
public class ThreadPoolConfig {

    @Bean(name = "scheduledHelperExecutor")
    public ThreadPoolTaskExecutor scheduledHelperExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);        // 核心线程数
        executor.setMaxPoolSize(20);         // 最大线程数
        executor.setQueueCapacity(200);      // 队列容量
        executor.setThreadNamePrefix("scheduled-executor"); // 线程名前缀
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy()); // 拒绝策略
        executor.setTaskDecorator(new TraceTaskDecorator()); // 任务装饰器（用于链路追踪）
        executor.initialize();
        return executor;
    }
}
```

**配置说明**:
- **核心线程数**: 池中始终保持的线程数量
- **最大线程数**: 池中允许的最大线程数量
- **队列容量**: 等待执行的任务队列大小
- **拒绝策略**: 当线程池和队列都满时的处理策略
- **任务装饰器**: 为任务添加额外功能（如链路追踪）

### 11. Redis分布式锁配置

#### 11.1 Redis连接配置

**开发环境配置** (`application-dev.yaml`):
```yaml
spring:
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

redisson:
  host: 127.0.0.1
  port: 6379
  database: 0
  username: ""
  password: ""
```

**生产环境配置** (`application-prod.yaml`):
```yaml
spring:
  redis:
    database: 0
    host: r-bp19gp3n8vhuw7kh1h.redis.rds.aliyuncs.com
    port: 6379
    username: hospital_chaperone
    password: gG8lM0lE5z
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

redisson:
  host: r-bp19gp3n8vhuw7kh1h.redis.rds.aliyuncs.com
  port: 6379
  database: 0
  username: hospital_chaperone
  password: gG8lM0lE5z
```

#### 11.2 分布式锁工作原理

1. **锁获取**: 每个定时任务执行前尝试获取Redis锁
2. **锁等待**: 最多等待3分钟获取锁
3. **任务执行**: 获取锁成功后执行任务
4. **锁释放**: 任务完成后自动释放锁

这确保了在多实例部署环境下，同一个定时任务不会重复执行。

## 定时任务监控和日志

### 12. 任务执行日志系统

#### 12.1 日志表结构

系统为每个定时任务的执行都会记录详细日志：

```sql
CREATE TABLE t_scheduled_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_type VARCHAR(50) NOT NULL COMMENT '任务类型',
    status INT NOT NULL COMMENT '执行状态（1-成功，2-失败）',
    cost_time BIGINT COMMENT '执行耗时（毫秒）',
    error_message TEXT COMMENT '错误信息',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间'
);
```

#### 12.2 日志记录流程

```java
public <T> void exec(ScheduledType scheduledType, Supplier<T> supplier) {
    // 1. 任务开始时创建日志记录
    ScheduledLogDO logDO = scheduledLogRepositoryService.create(scheduledType);
    long start = System.currentTimeMillis();

    try {
        // 2. 执行任务
        threadPoolTaskExecutor.submit(() -> supplier.get());

        // 3. 任务成功时更新日志
        long cost = System.currentTimeMillis() - start;
        ScheduledLogDO updateEntity = logDO.successByLock(cost);
        scheduledLogRepositoryService.updateById(updateEntity);

    } catch (Exception e) {
        // 4. 任务失败时记录错误信息
        long cost = System.currentTimeMillis() - start;
        ScheduledLogDO updateEntity = logDO.failByException(cost);
        scheduledLogRepositoryService.updateById(updateEntity);
    }
}
```

#### 12.3 日志查询和监控

可以通过查询日志表来监控定时任务的执行情况：

```sql
-- 查询最近24小时的任务执行情况
SELECT
    task_type,
    status,
    COUNT(*) as execution_count,
    AVG(cost_time) as avg_cost_time,
    MAX(cost_time) as max_cost_time
FROM t_scheduled_log
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY task_type, status
ORDER BY task_type;

-- 查询失败的任务
SELECT * FROM t_scheduled_log
WHERE status = 2
ORDER BY created_at DESC
LIMIT 10;
```

## 测试和调试

### 13. 单元测试

#### 13.1 定时任务测试示例

**文件位置**: `chaperone-service/src/test/java/com/avatar/hospital/chaperone/job/plan/PatrolPlanSchedulingTest.java`

```java
@SpringBootTest(classes = TestApplication.class)
public class PatrolPlanSchedulingTest {

    @Autowired
    private PatrolPlanScheduling patrolPlanScheduling;

    @Autowired
    private JobTaskRegister jobTaskRegister;

    @Test
    public void testExecute() {
        // 直接测试任务执行逻辑
        Boolean result = patrolPlanScheduling.execute(20231201, 10);
        assertThat(result).isTrue();
    }

    @Test
    public void testManualTrigger() {
        // 测试通过任务注册器手动触发
        Object[] args = {20231201, 10};
        Boolean result = jobTaskRegister.invoke("patrol-plan-task", args);
        assertThat(result).isTrue();
    }

    @Test
    public void testScheduledMethod() {
        // 测试定时任务方法（不会真正按时间触发）
        assertDoesNotThrow(() -> {
            patrolPlanScheduling.planTask();
        });
    }
}
```

#### 13.2 集成测试

```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.task.scheduling.enabled=true"  // 确保定时任务启用
})
public class ScheduledTaskIntegrationTest {

    @Autowired
    private ScheduledLogRepositoryService scheduledLogRepositoryService;

    @Test
    public void testTaskExecution() {
        // 手动触发任务并验证日志记录
        // 这里可以验证任务是否正确执行并记录了日志
    }
}
```

## 常见问题和解决方案

### 14. 定时任务不执行

**问题现象**: 定时任务到了执行时间但没有执行

**可能原因和解决方案**:

1. **未启用定时任务**
   ```java
   // 检查主启动类是否有@EnableScheduling注解
   @EnableScheduling  // 确保有这个注解
   @SpringBootApplication
   public class HospitalChaperoneWebApplication { ... }
   ```

2. **类未被Spring管理**
   ```java
   @Component  // 确保有这个注解
   public class PatrolPlanScheduling { ... }
   ```

3. **Cron表达式错误**
   ```java
   // 错误的表达式
   @Scheduled(cron = "0 0 1 * *")  // 缺少周字段

   // 正确的表达式
   @Scheduled(cron = "0 0 1 * * ?")  // 每天凌晨1点
   ```

4. **分布式锁获取失败**
   - 检查Redis连接状态
   - 查看日志中是否有"获取锁失败"的信息
   - 确认锁的超时时间设置合理

### 15. 任务重复执行

**问题现象**: 同一个任务在多个实例上同时执行

**解决方案**:

1. **检查Redis配置**
   ```yaml
   spring:
     redis:
       host: 正确的Redis地址
       port: 6379
       database: 0
   ```

2. **确认分布式锁正常工作**
   ```java
   // 检查ScheduledHelper中的锁逻辑
   RLock lock = redissonClient.getLock(scheduledType.getLockKey());
   if (!lock.tryLock(3, TimeUnit.MINUTES)) {
       return; // 获取锁失败，任务不执行
   }
   ```

### 16. 任务执行异常

**问题现象**: 任务执行过程中抛出异常

**排查步骤**:

1. **查看执行日志**
   ```sql
   SELECT * FROM t_scheduled_log
   WHERE status = 2 AND task_type = 'PATROL_PLAN_TASK_DAY'
   ORDER BY created_at DESC;
   ```

2. **检查应用日志**
   ```bash
   # 查看应用日志中的异常信息
   grep -i "ScheduledHelper.*fail" application.log
   ```

## 总结

### 17. 系统核心特性

医院陪护系统的定时任务框架具有以下核心特性：

1. **统一管理**: 通过@EnableScheduling统一启用，JobTaskRegister统一注册管理
2. **分布式执行**: 使用Redis分布式锁确保多实例环境下任务不重复执行
3. **异步处理**: 使用专用线程池异步执行任务，提高系统响应性和稳定性
4. **完整监控**: 记录任务执行的完整生命周期，包括执行时间、状态、异常信息
5. **灵活控制**: 支持全局和单个任务的启停控制，支持手动触发
6. **易于扩展**: 标准化的任务添加流程，只需几个步骤即可添加新的定时任务

### 18. 完整执行流程

```
定时任务完整执行流程：
1. 应用启动 → @EnableScheduling启用定时任务
2. Spring扫描 → 发现@Scheduled方法并注册
3. JobTaskRegister → 扫描@Task注解方法并注册到任务注册表
4. 定时触发 → Spring按Cron表达式触发@Scheduled方法
5. 获取锁 → ScheduledHelper尝试获取Redis分布式锁
6. 创建日志 → 在数据库中创建任务执行日志记录
7. 异步执行 → 使用线程池异步执行具体业务逻辑
8. 更新日志 → 根据执行结果更新日志状态和耗时
9. 释放锁 → 无论成功失败都释放分布式锁
10. 等待下次 → 等待下一次定时触发
```

### 19. 最佳实践建议

1. **任务设计**: 每个定时任务应该是幂等的，重复执行不会产生副作用
2. **异常处理**: 在业务逻辑中添加完善的异常处理，避免任务因异常而中断
3. **性能监控**: 定期检查任务执行日志，监控任务执行时间和成功率
4. **资源管理**: 合理配置线程池大小，避免任务执行影响系统性能
5. **测试验证**: 新增任务后要进行充分测试，包括单元测试和集成测试

这套定时任务系统为医院陪护业务提供了可靠的自动化支持，确保了巡检计划、维保计划等关键业务的及时执行。

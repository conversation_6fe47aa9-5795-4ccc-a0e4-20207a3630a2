package com.avatar.hospital.chaperone.admin.service;

import com.avatar.hospital.chaperone.admin.vo.TenantInfo;
import com.avatar.hospital.chaperone.admin.vo.TenantStatistics;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 租户管理服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface TenantManageService {

    /**
     * 获取租户列表（分页）
     * 
     * @param current 当前页
     * @param size 页大小
     * @param keyword 搜索关键词
     * @return 租户列表
     */
    Page<TenantInfo> getTenantList(Integer current, Integer size, String keyword);

    /**
     * 获取租户详情
     * 
     * @param tenantId 租户ID
     * @return 租户详情
     */
    TenantInfo getTenantDetail(Long tenantId);

    /**
     * 获取租户统计信息
     * 
     * @param tenantId 租户ID
     * @return 统计信息
     */
    TenantStatistics getTenantStatistics(Long tenantId);

    /**
     * 获取所有租户的统计概览
     * 
     * @return 所有租户统计信息
     */
    List<TenantStatistics> getAllTenantsStatistics();

    /**
     * 切换租户状态
     * 
     * @param tenantId 租户ID
     * @return 操作结果
     */
    boolean toggleTenantStatus(Long tenantId);

    /**
     * 重置租户数据（危险操作）
     * 
     * @param tenantId 租户ID
     * @return 操作结果
     */
    boolean resetTenantData(Long tenantId);

    /**
     * 导出租户数据
     * 
     * @param tenantId 租户ID
     * @return 导出文件路径
     */
    String exportTenantData(Long tenantId);

    /**
     * 获取跨租户数据统计
     * 
     * @return 跨租户统计信息
     */
    TenantStatistics getCrossTenantStatistics();

}

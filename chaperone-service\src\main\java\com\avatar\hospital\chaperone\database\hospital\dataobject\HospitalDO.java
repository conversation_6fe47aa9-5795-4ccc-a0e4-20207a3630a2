package com.avatar.hospital.chaperone.database.hospital.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;


/**
 * 院区信息数据对象
 */

@Getter
@Setter
@TableName("hospital")
public class HospitalDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 院区名称
     */
    @TableField("name")
    private String name;

    /**
     * 院区地址
     */
    @TableField("address")
    private String address;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 启用状态: 0=>禁用; 1=>启用
     */
    @TableField("enable")
    private Integer enable;

}

package com.avatar.hospital.chaperone.service.plan.impl;

import com.avatar.hospital.chaperone.builder.plan.PlanBuilder;
import com.avatar.hospital.chaperone.builder.plan.PlanTaskBuilder;
import com.avatar.hospital.chaperone.component.baccount.OrganizationComponent;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanTaskDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.database.plan.enums.TaskStatusType;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRefExecutorRepositoryAdaptor;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRefOrgRepositoryAdaptor;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRepositoryAdaptor;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanTaskRepositoryAdaptor;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormBizType;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormSourceType;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.request.plan.QueryRequest;
import com.avatar.hospital.chaperone.request.plan.TaskExecuteRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormCreateRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormPageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.plan.PlanTaskVO;
import com.avatar.hospital.chaperone.response.plan.PlanVO;
import com.avatar.hospital.chaperone.response.repair.RepairFormResponse;
import com.avatar.hospital.chaperone.service.baccount.WebAccountService;
import com.avatar.hospital.chaperone.service.device.DeviceService;
import com.avatar.hospital.chaperone.service.plan.PlanTaskService;
import com.avatar.hospital.chaperone.service.repair.RepairFormService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 13:32
 */
@Service("PlanTaskService")
public class PlanTaskServiceImpl implements PlanTaskService {
    @Autowired
    PlanTaskRepositoryAdaptor planTaskRepositoryService;
    @Autowired
    RepairFormService repairFormService;
    @Autowired
    PlanRefOrgRepositoryAdaptor refOrgRepositoryService;
    @Autowired
    PlanRefExecutorRepositoryAdaptor refExecutorRepositoryService;
    @Autowired
    PlanRepositoryAdaptor planRepositoryService;
    @Autowired
    OrganizationComponent organizationComponent;
    @Autowired
    WebAccountService accountService;
    @Autowired
    DeviceService deviceService;

    @Override
    public Boolean create(PlanType planType, PlanDO planDO, List<Long> deviceIds) {

        if (exist(planType, planDO.getId())) {
            return true;
        }
        List<PlanTaskDO> PlanTaskDOs = PlanBuilder.buildTask(planType, planDO, deviceIds);

        planTaskRepositoryService.saveBatch(planType, PlanTaskDOs);

        return true;
    }

    private boolean exist(PlanType planType, Long planId) {
        LambdaQueryWrapper<PlanTaskDO> queryWrapper = planTaskRepositoryService.queryWrapper(planType);
        queryWrapper.eq(PlanTaskDO::getPlanId, planId);
        queryWrapper.gt(PlanTaskDO::getCreatedAt, DateUtils.dateStart()).lt(PlanTaskDO::getCreatedAt, DateUtils.dateEnd());
        return planTaskRepositoryService.list(planType, queryWrapper).size() > 0;
    }

    @Override
    public PageResponse<PlanTaskVO> paging(QueryRequest request) {
        Page<PlanTaskDO> page = request.ofPage();
        LambdaQueryWrapper<PlanTaskDO> queryWrapper = planTaskRepositoryService.queryWrapper(request.getPlanType());
        queryWrapper.orderByDesc(PlanTaskDO::getId);
        queryWrapper.eq(Objects.nonNull(request.getPlanId()), PlanTaskDO::getPlanId, request.getPlanId());
        queryWrapper.eq(Objects.nonNull(request.getCode()), PlanTaskDO::getCode, request.getCode());
        queryWrapper.like(Objects.nonNull(request.getName()), PlanTaskDO::getName, request.getName());
        queryWrapper.eq(Objects.nonNull(request.getStateTime()), PlanTaskDO::getCreatedAt, request.getStateTime());
        queryWrapper.eq(Objects.nonNull(request.getEndTime()), PlanTaskDO::getCreatedAt, request.getEndTime());

        queryWrapper.orderByDesc(PlanTaskDO::getCreatedAt);

        Set<Long> planIds = Sets.newHashSet();
        if (Objects.nonNull(request.getAccountId())) {
            Set<Long> accountPlanIds = refExecutorRepositoryService.getPlanByAccount(request.getPlanType(), request.getAccountId());
            if (CollectionUtils.isNotEmpty(accountPlanIds)) {
                planIds.addAll(accountPlanIds);
            }
        }
        if (CollectionUtils.isNotEmpty(request.getOrgIds())) {
            Set<Long> orgPlanIds = refOrgRepositoryService.getPlanByOrg(request.getPlanType(), request.getOrgIds());
            if (CollectionUtils.isNotEmpty(orgPlanIds)) {
                planIds.addAll(orgPlanIds);
            }
        }
        queryWrapper.in(CollectionUtils.isNotEmpty(planIds), PlanTaskDO::getPlanId, planIds);

        page = planTaskRepositoryService.page(request.getPlanType(), page, queryWrapper);

        PageResponse<PlanTaskVO> pageResponse = PageResponse.build(page, PlanTaskBuilder::build);
        //设置设备
        setDevice(pageResponse.getRecords());
        //设置计划
        setPlan(request.getPlanType(), pageResponse.getRecords());
        //设置报修单
        setRepairForm(request.getPlanType(), pageResponse.getRecords());
        return pageResponse;
    }

    private void setDevice(List<PlanTaskVO> planTaskVOS) {
        if (CollectionUtils.isEmpty(planTaskVOS)) {
            return;
        }
        Set<Long> deviceIds = planTaskVOS.stream().map(PlanTaskVO::getDeviceId).collect(Collectors.toSet());
        Map<Long, String> deviceMap = deviceService.findDeviceMap(deviceIds);
        planTaskVOS.stream().forEach(o -> {
            o.setDeviceName(deviceMap.get(o.getDeviceId()));
        });
    }

    private void setPlan(PlanType planType, List<PlanTaskVO> planTaskVOS) {
        if (CollectionUtils.isEmpty(planTaskVOS)) {
            return;
        }
        Set<Long> planIds = planTaskVOS.stream().map(PlanTaskVO::getPlanId).collect(Collectors.toSet());
        LambdaQueryWrapper<PlanDO> queryWrapper = planRepositoryService.queryWrapper(planType);
        queryWrapper.eq(PlanDO::getDeleted, DeletedEnum.NO.getStatus());
        queryWrapper.in(PlanDO::getId, planIds);
        List<PlanDO> planDOS = planRepositoryService.list(planType, queryWrapper);
        if (CollectionUtils.isEmpty(planDOS)) {
            return;
        }
        Map<Long, PlanDO> planDOMap = planDOS.stream().collect(Collectors.toMap(PlanDO::getId, Function.identity()));

        //  Set<Long> belongOrgIds = planDOS.stream().map(PlanDO::getOrgId).collect(Collectors.toSet());
        Set<Long> orgIds = refOrgRepositoryService.getRefIds(planType, planIds);
//        if (CollectionUtils.isNotEmpty(orgIds)) {
//            belongOrgIds.addAll(orgIds);
//        }
        Map<Long, String> orgMap = organizationComponent.findOrgMap(orgIds);

        Set<Long> accountIds = refExecutorRepositoryService.getRefIds(planType, planIds);
        Map<Long, String> accountMap = accountService.getAccountMap(accountIds);

        planTaskVOS.stream().forEach(o -> {
            PlanDO planDO = planDOMap.get(o.getPlanId());
            if (Objects.nonNull(planDO)) {
                o.setPlanCode(planDO.getCode());
                o.setPlanRemark(planDO.getRemark());
                o.setExecutors(PlanVO.map2List(getSubMap(accountMap, refExecutorRepositoryService.getRefIds(planType, planDO.getId()))));
                o.setDepartments(PlanVO.map2List(getSubMap(orgMap, refOrgRepositoryService.getRefIds(planType, planDO.getId()))));
            }
        });
    }

    private Map<Long, String> getSubMap(Map<Long, String> map, Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids) || Objects.isNull(map)) {
            return Maps.newHashMap();
        }
        Map<Long, String> subMap = Maps.newHashMap();
        for (Long id : ids) {
            subMap.put(id, map.get(id));
        }
        return subMap;
    }

    private void setRepairForm(PlanType planType, List<PlanTaskVO> planTaskVOS) {
        if (CollectionUtils.isEmpty(planTaskVOS)) {
            return;
        }
        Set<Long> taskIds = planTaskVOS.stream().map(PlanTaskVO::getId).collect(Collectors.toSet());
        RepairFormPageRequest request = new RepairFormPageRequest();
        request.setPageIndex(1);
        request.setPageSize(100);
        if (PlanType.PATROL.equals(planType)) {
            request.setBizType(RepairFormBizType.ROUTING_INSPECTION.getStatus());

        } else {
            request.setBizType(RepairFormBizType.MAINTENANCE.getStatus());

        }
        request.setBizIds(taskIds);
        PageResponse<RepairFormResponse> repairResponse = repairFormService.paging(request);
        if (CollectionUtils.isEmpty(repairResponse.getRecords())) {
            return;
        }
        Map<Long, List<RepairFormResponse>> repairFormResponseMap = repairResponse.getRecords().stream().collect(Collectors.groupingBy(RepairFormResponse::getBizId));
        for (PlanTaskVO planTaskVO : planTaskVOS) {

            List<RepairFormResponse> formResponses = repairFormResponseMap.get(planTaskVO.getId());
            if (CollectionUtils.isEmpty(formResponses)) {
                continue;
            }
            RepairFormResponse repairForm = formResponses.get(0);
            planTaskVO.setRepairFormCode(repairForm.getCode());
            planTaskVO.setRepairFormId(repairForm.getId());
        }
    }

    /**
     * 删除任务
     *
     * @param request
     * @return
     */
    @Override
    public boolean update2delete(PlanRequest request) {
        return planTaskRepositoryService.update2delete(request);
    }

    /**
     * 获取今天之前的未完成的所有任务
     *
     * @param planId
     * @return
     */
    @Override
    public List<PlanTaskVO> getPlanBeforeTodayTask(PlanType planType, Long planId) {
        Page<PlanTaskDO> page = new Page<>(1, 1000);
        LambdaQueryWrapper<PlanTaskDO> queryWrapper = planTaskRepositoryService.queryWrapper(planType);
        queryWrapper.eq(PlanTaskDO::getStatus, TaskStatusType.NON_COMPLETED.getCode());
        queryWrapper.eq(PlanTaskDO::getPlanId, planId);
        queryWrapper.orderByDesc(PlanTaskDO::getCreatedAt);
        page = planTaskRepositoryService.page(planType, page, queryWrapper);
        return PageResponse.build(page, PlanTaskBuilder::build).getRecords();
    }

    /**
     * 获取今天之前的未完成的所有任务 - 不带租户隔离
     *
     * @param planType
     * @param planId
     * @return
     */
    @Override
    public List<PlanTaskVO> getPlanBeforeTodayTaskWithoutTenant(PlanType planType, Long planId) {
        LambdaQueryWrapper<PlanTaskDO> queryWrapper = planTaskRepositoryService.queryWrapper(planType);
        queryWrapper.eq(PlanTaskDO::getStatus, TaskStatusType.NON_COMPLETED.getCode());
        queryWrapper.eq(PlanTaskDO::getPlanId, planId);
        queryWrapper.orderByDesc(PlanTaskDO::getCreatedAt);
        queryWrapper.last("LIMIT 1000");
        return planTaskRepositoryService.listWithoutTenant(planType, queryWrapper);
    }

    /**
     * 任务执行
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean execute(TaskExecuteRequest request) {
        PlanTaskDO taskDO = planTaskRepositoryService.getById(request.getPlanType(), request.getId());
        AssertUtils.isNotNull(taskDO, ErrorCode.PROJECT_PLAN_TASK_NOT_EXIST);
        planTaskRepositoryService.execute(request);
        //2.报修
        if (Objects.equals(Boolean.TRUE, request.getIsRepair())) {
            RepairFormCreateRequest repairFormCreateRequest = request.getRepairFormCreateRequest();
            repairFormCreateRequest.setBizId(request.getId());
            if (PlanType.PATROL.equals(request.getPlanType())) {
                repairFormCreateRequest.setBizType(RepairFormBizType.ROUTING_INSPECTION.getStatus());
            } else {
                repairFormCreateRequest.setBizType(RepairFormBizType.MAINTENANCE.getStatus());
            }
            repairFormCreateRequest.setSourceType(RepairFormSourceType.H5.getStatus());
            repairFormCreateRequest.setOperatorUser(request.getOperatorUser());
            repairFormService.create(repairFormCreateRequest);
        }
        return true;
    }

    /**
     * 任务过期
     *
     * @param taskIds
     * @return
     */
    @Override
    public Boolean expired(PlanType planType, List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return false;
        }
        return planTaskRepositoryService.expired(planType, taskIds);

    }
}

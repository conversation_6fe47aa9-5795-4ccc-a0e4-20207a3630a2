package com.avatar.hospital.chaperone.service.baccount;

import com.avatar.hospital.chaperone.request.baccount.OrganizationAddRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationHospitalRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationTreeRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationUpdateRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAdministrativeOfficeQueryRequest;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAdministrativeOfficeQueryRequest;
import com.avatar.hospital.chaperone.response.baccount.OrganizationAddResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationHospitalResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationTreeResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationUpdateResponse;
import com.avatar.hospital.chaperone.response.caccount.ConsumerOrganizationHospitalResponse;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
public interface OrganizationService {

    /**
     * 添加组织机构
     *
     * @param request -
     * @return -
     */
    OrganizationAddResponse add(OrganizationAddRequest request);

    /**
     * 更新组织机构
     *
     * @param request -
     * @return -
     */
    OrganizationUpdateResponse update(OrganizationUpdateRequest request);

    /**
     * 删除组织机构
     *
     * @param request -
     * @return -
     */
    OrganizationDeleteResponse delete(OrganizationDeleteRequest request);


    /**
     * 护球组织树形结构
     */
    List<OrganizationTreeResponse> getTree();


    /**
     * B端查询组织机构树形结构
     * <p>1.如果是管理员返回所有组织机构</p>
     * <p>2.如果用户有查看组织机构树的角色，返回所有组织机构</p>
     * <p>3.根据当前用户所在的医院&部门层级查询 只能看到自己所在的医院/部门层级和以下节点</p>
     *
     * @param request -
     * @return -
     */
    List<OrganizationTreeResponse> accountTree(OrganizationTreeRequest request);

    /**
     * B端查询医院层级组织机构
     * <p>1.如果是管理员返回所有医院层级列表</p>
     * <p>2.如果用户有查看所有医院层级的角色，返回所有医院层级列表</p>
     * <p>3.根据当前用户所在的医院层级查询 只能看到自己所在的医院层级</p>
     *
     * @param request -
     * @return -
     */
    List<OrganizationHospitalResponse> hospitalOrganizationList(OrganizationHospitalRequest request);

    /**
     * B端查询部门层级组织机构
     * <p>1.如果是管理员返回所有部门层级列表</p>
     * <p>2.如果用户有查看所有部门层级的角色，返回所有部门层级列表</p>
     * <p>3.根据当前用户所在的部门层级查询 只能看到自己所在的部门层级</p>
     *
     * @param request -
     * @return -
     */
    List<OrganizationHospitalResponse> departmentOrganizationList(OrganizationHospitalRequest request);

    /**
     * B端根据医院ID查询科室列表
     *
     * @param request -
     * @return -
     */
    List<OrganizationHospitalResponse> webAdministrativeOfficeOrganizationList(WebAdministrativeOfficeQueryRequest request);

    /**
     * C端查询医院层级列表
     *
     * @return -
     */
    List<ConsumerOrganizationHospitalResponse> consumerHospitalOrganizationList();

    /**
     * C端根据医院ID查询科室列表
     *
     * @param request -
     * @return -
     */
    List<ConsumerOrganizationHospitalResponse> consumerAdministrativeOfficeOrganizationList(ConsumerAdministrativeOfficeQueryRequest request);
}

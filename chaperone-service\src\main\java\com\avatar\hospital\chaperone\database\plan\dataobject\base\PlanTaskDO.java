package com.avatar.hospital.chaperone.database.plan.dataobject.base;

import com.avatar.hospital.chaperone.database.part.dataobject.base.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 巡检计划任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
public class PlanTaskDO extends TenantBaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务编号（字母+日期）
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态（0-未完成，1-已完成，2-已过期）
     */
    private Integer status;

    /**
     * 计划ID
     */
    private Long planId;
    /**
     * 设备ID
     */
    private Long deviceId;
    /**
     * 是否需要报修
     */
    private Boolean isRepair;

    /**
     * 备注
     */
    private String remark;

    /**
     * 完成时间
     */
    private LocalDateTime completedTime;

}

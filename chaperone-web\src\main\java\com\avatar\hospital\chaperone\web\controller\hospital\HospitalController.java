package com.avatar.hospital.chaperone.web.controller.hospital;


import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.request.hospital.HospitalAddRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalDeleteRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalPagingRequest;
import com.avatar.hospital.chaperone.request.hospital.HospitalUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.hospital.*;
import com.avatar.hospital.chaperone.service.hospital.HospitalService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.web.validator.hospital.HospitalValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v1/web/hospital")
@RequiredArgsConstructor
public class HospitalController {

    private final HospitalService hospitalService;

    /**
     * 创建
     */
    @PostMapping
    public SingleResponse<HospitalAddResponse> add(@RequestBody HospitalAddRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("hospital add request:{}", request);
            HospitalValidator.addValidator(request);
            return hospitalService.add(request);
        });
    }

    /**
     * 更新
     */
    @PutMapping
    public SingleResponse<HospitalUpdateResponse> update(@RequestBody HospitalUpdateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("hospital update request:{}", request);
            HospitalValidator.updateValidator(request);
            return hospitalService.update(request);
        });
    }

    /**
     * 分页
     *
     * @param request
     * @return
     */
    @GetMapping(value = "paging")
    public SingleResponse<PageResponse<HospitalPagingResponse>> paging(HospitalPagingRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("hospital paging request:{}", request);
            HospitalValidator.pagingValidate(request);
            return hospitalService.paging(request);
        });
    }

    /**
     * 删除
     */
    @DeleteMapping(value = "delete")
    public SingleResponse<HospitalDeleteResponse> delete(@RequestBody HospitalDeleteRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("hospital delete request:{}", request);
            return null;
        });
    }

    /**
     * 获取所有可用院区
     */
    @GetMapping(value = "resourceData")
    public SingleResponse<List<HospitalResourceDataResponse>> resourceData() {
        return TemplateProcess.doProcess(log, hospitalService::getResourceData);
    }

}

package com.avatar.hospital.chaperone.web.controller.repair;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.request.repair.RepairFormFeedbackPageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.repair.RepairFormFeedbackResponse;
import com.avatar.hospital.chaperone.service.repair.RepairFormFeedbackService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * B端保修单-反馈
 *
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 13:41
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/repair-feedback")
public class RepairFormFeedbackController {
    private final RepairFormFeedbackService repairFormFeedbackService;

    /**
     * 查询
     *
     * @param request
     * @return
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<RepairFormFeedbackResponse>> paging(@RequestBody RepairFormFeedbackPageRequest request) {
        return TemplateProcess.doProcess(log, () -> repairFormFeedbackService.paging(request));
    }

}

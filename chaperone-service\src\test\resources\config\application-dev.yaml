# Spring Boot 应用配置
spring:
  application:
    name: chaperone-web                     # 应用名称：用于服务注册、日志标识等

  # 数据源配置
  datasource:
    # MySQL数据库连接URL，包含重要参数：
    # - useSSL=false: 禁用SSL连接(开发环境)
    # - useUnicode=true&characterEncoding=UTF-8: 启用Unicode和UTF-8编码
    # - allowMultiQueries=true: 允许一次执行多条SQL语句
    # - zeroDateTimeBehavior=convertToNull: 零日期转换为null
    # - serverTimezone=Asia/Shanghai: 设置时区为上海
    # - allowPublicKeyRetrieval=true: 允许客户端从服务器获取公钥
    url: *************************************************************************************************************************************************************************************************************************
    username: root                          # 数据库用户名
    password: 123456                        # 数据库密码
    type: com.alibaba.druid.pool.DruidDataSource  # 使用Druid连接池
    driver-class-name: com.mysql.cj.jdbc.Driver  # MySQL 8.x 驱动类
    druid:
      # 连接池配置
      maxActive: 20                           # 最大连接数：连接池中同时存在的最大连接数
      initialSize: 1                          # 初始连接数：应用启动时创建的连接数
      maxWait: 60000                          # 最大等待时间：获取连接时的最大等待时间(毫秒)，超时抛异常
      minIdle: 1                              # 最小空闲连接数：连接池中保持的最小空闲连接数

      # 连接回收配置
      timeBetweenEvictionRunsMillis: 60000    # 回收检测间隔：每60秒检测一次需要回收的连接
      minEvictableIdleTimeMillis: 300000      # 最小空闲时间：连接空闲5分钟后可被回收

      # 连接有效性检测
      validationQuery: select 'x'             # 验证查询：用于检测连接是否有效的SQL语句
      testWhileIdle: true                     # 空闲时检测：在空闲时检测连接有效性
      testOnBorrow: false                     # 借用时检测：获取连接时不检测(性能考虑)
      testOnReturn: false                     # 归还时检测：归还连接时不检测(性能考虑)

      # 预编译语句缓存
      poolPreparedStatements: true            # 启用PSCache：缓存预编译语句提高性能
      maxOpenPreparedStatements: 20           # PSCache大小：每个连接最多缓存20个预编译语句

      # 连接初始化
      connection-init-sqls: set names utf8mb4; # 连接初始化SQL：设置字符集支持emoji等4字节字符

  # Redis 缓存配置
  redis:
    database: 1                             # Redis数据库索引：使用1号数据库(默认为0)
    host: 127.0.0.1                        # Redis服务器地址：本地开发环境
    port: 6379                              # Redis服务器端口：默认端口
    # password:                             # Redis密码：开发环境无密码
    timeout: 10s                            # 连接超时时间：10秒超时
    lettuce: # Lettuce连接池配置(Spring Boot 2.x默认)
      pool:
        max-active: 200                     # 最大连接数：连接池最大活跃连接数
        max-wait: -1ms                      # 最大等待时间：-1表示无限等待
        max-idle: 10                        # 最大空闲连接：连接池中最大空闲连接数
        min-idle: 0                         # 最小空闲连接：连接池中最小空闲连接数

# Sa-Token 权限认证框架配置
sa-token:
  token-name: ${spring.application.name}-token  # Token名称：使用应用名作为前缀，避免冲突
  timeout: 2592000                          # Token有效期：30天(2592000秒)，-1表示永久有效
  active-timeout: -1                        # 活跃超时：-1表示不限制，Token永不因不活跃而冻结
  is-concurrent: true                       # 并发登录：允许同一账号在多个地方同时登录
  is-share: true                            # 共享Token：多地登录时共用一个Token(节省内存)
  token-style: uuid                         # Token风格：使用UUID格式(安全性较高)
  is-log: true                              # 操作日志：启用Sa-Token操作日志输出

# Redisson 分布式锁配置
redisson:
  host: 127.0.0.1                          # Redis主机地址：本地开发环境
  port: 6379                                # Redis端口：默认端口
  database: 0                               # Redis数据库：使用0号数据库(与Spring Redis分离)
  username: ""                              # Redis用户名：开发环境为空
  password: ""                              # Redis密码：开发环境为空

# UID 分布式ID生成器配置
uid:
  timeBits: 28                              # 时间位数：28位时间戳(约8.5年)
  workerBits: 22                            # 机器位数：22位机器ID(支持400万台机器)
  seqBits: 13                               # 序列位数：13位序列号(每毫秒8192个ID)
  epochStr: "2023-08-01"                    # 起始时间：ID生成器的时间起点(影响使用寿命)
  type: standard                            # 生成器类型：standard(标准版) 或 cached(缓存版)
  db:
    switch: rds                             # 数据源类型：rds(关系数据库) 或 mongo(MongoDB)

# ==================== 微信相关配置 ====================

# 微信模板消息业务配置
wx-template-message-biz:
  orderPayTemplateId: 'ItjjzopGdCyrTz_mdJBnczFlmycCkjX1W_4ZkIYuBZk'  # 订单支付模板消息ID

# 微信开发配置
wx:
  # 微信小程序配置
  miniapp:
    appid: wxa0360fe3b2279192                # 微信小程序AppID
    secret: 00595355d5264039d336dfc2501ed50b  # 微信小程序AppSecret
    configStorage: # 配置存储方式
      type: RedisTemplate                   # 使用Redis存储配置(支持集群)
      keyPrefix: wx-java-wa                 # Redis键前缀：微信小程序配置

  # 微信公众号配置
  mp:
    appid: wxe3a51a4f491972a7                # 微信公众号AppID
    secret: 5562eaeec5ee9c181a4d908fdec9191c  # 微信公众号AppSecret
    configStorage: # 配置存储方式
      type: RedisTemplate                   # 使用Redis存储配置(支持集群)
      keyPrefix: wx-java-wx                 # Redis键前缀：微信公众号配置

  # 微信支付配置
  pay:
    appId: "wx857af75a951372a8"             # 微信支付关联的AppID
    mchId: 1533433711                       # 微信支付商户号
    mchKey: wJ65GOBWhqCXc406PJF6XQth7VqWZ18j # 商户密钥(API密钥)
    keyPath: classpath:cert/apiclient_cert_1533433711.p12  # 商户证书文件路径
    certSerialNo: 3C456C7350309A2C22D3261A5896878DB0C0417E  # 证书序列号
    privateKeyPath: classpath:cert/apiclient_key.pem        # 私钥文件路径
    privateCertPath: classpath:cert/apiclient_wx_cert.pem   # 证书文件路径
    notifyUrl: 'https://localhsot:8080/api/v1/consumer/notify/wx'  # 支付回调地址

# 阿里云OSS对象存储配置
oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com    # OSS服务端点：杭州区域
  bucketName: chery-sit-new                 # 存储桶名称：测试环境存储桶
  accessKeyId: LTAIhgw64KORhJ1p            # 访问密钥ID：阿里云AccessKey
  secretAccessKey: SvYNOo11O305iesvbxlY7lvQqUKGFn  # 访问密钥Secret

# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    log-format: "\ntime:%(executionTime) || sql:%(sql)\n"    # 日志格式
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # SQL日志输出：控制台输出(开发环境)


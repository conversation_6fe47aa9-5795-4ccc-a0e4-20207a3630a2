# @InterceptorIgnore注解问题解决方案

## 🔍 问题描述

在使用MyBatis-Plus多租户插件时，发现 `@InterceptorIgnore(tenantLine = "true")` 注解没有起作用，查询语句仍然被添加了 `hid = -1` 条件。

## 🔍 问题原因分析

### 1. 注解位置错误

**错误的做法**：在Service层方法上使用 `@InterceptorIgnore` 注解

```java
// ❌ 错误：在Service层使用注解无效
@Service
public class WebAccountRepositoryServiceImpl {
    
    @Override
    @InterceptorIgnore(tenantLine = "true")  // ❌ 这里不会生效
    public AccountDO findByPhoneNumberAndHidIgnoreTenant(String phoneNumber, long hid) {
        LambdaQueryWrapper<AccountDO> queryWrapper = queryWrapper();
        queryWrapper.eq(AccountDO::getPhoneNumber, phoneNumber);
        queryWrapper.eq(AccountDO::getHid, hid);
        return baseMapper.selectOne(queryWrapper);  // 仍然会被租户插件拦截
    }
}
```

### 2. 根本原因

- `@InterceptorIgnore` 注解只在 **Mapper层** 生效
- MyBatis-Plus的租户插件是在SQL执行层面进行拦截的
- Service层的注解无法影响底层的SQL生成过程

## ✅ 正确解决方案

### 方案1: 在Mapper层添加自定义方法（推荐）

#### 1.1 在Mapper接口中添加自定义方法

```java
@Mapper
public interface WebAccountMapper extends BaseMapper<AccountDO> {

    /**
     * 根据手机号和院区ID查询用户（忽略租户隔离）
     * 注意：@InterceptorIgnore注解必须在Mapper方法上才能生效
     */
    @InterceptorIgnore(tenantLine = "true")
    @Select("SELECT * FROM t_b_account WHERE phone_number = #{phoneNumber} AND hid = #{hid} AND deleted = 0")
    AccountDO selectByPhoneNumberAndHidIgnoreTenant(@Param("phoneNumber") String phoneNumber, @Param("hid") Long hid);
}
```

#### 1.2 在Service层调用Mapper方法

```java
@Service
public class WebAccountRepositoryServiceImpl {
    
    @Override
    public AccountDO findByPhoneNumberAndHidIgnoreTenant(String phoneNumber, long hid) {
        if (StringUtils.isBlank(phoneNumber)) {
            return null;
        }
        // ✅ 正确：直接调用Mapper的忽略租户隔离的方法
        return baseMapper.selectByPhoneNumberAndHidIgnoreTenant(phoneNumber, hid);
    }
}
```

### 方案2: 使用XML配置（适用于复杂查询）

#### 2.1 创建XML映射文件

```xml
<!-- WebAccountMapper.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.avatar.hospital.chaperone.database.baccount.mapper.WebAccountMapper">
    
    <!-- 忽略租户隔离的查询 -->
    <select id="selectByPhoneNumberAndHidIgnoreTenant" resultType="com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO">
        SELECT * FROM t_b_account 
        WHERE phone_number = #{phoneNumber} 
        AND hid = #{hid} 
        AND deleted = 0
    </select>
    
</mapper>
```

#### 2.2 在Mapper接口中声明方法

```java
@Mapper
public interface WebAccountMapper extends BaseMapper<AccountDO> {

    @InterceptorIgnore(tenantLine = "true")
    AccountDO selectByPhoneNumberAndHidIgnoreTenant(@Param("phoneNumber") String phoneNumber, @Param("hid") Long hid);
}
```

### 方案3: 临时清除租户上下文（不推荐）

```java
@Service
public class WebAccountRepositoryServiceImpl {
    
    public AccountDO findByPhoneNumberAndHidIgnoreTenant(String phoneNumber, long hid) {
        // ⚠️ 不推荐：临时清除租户上下文
        UserContext.clear();
        try {
            LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AccountDO::getDeleted, DeletedEnum.NO.getStatus());
            queryWrapper.eq(AccountDO::getPhoneNumber, phoneNumber);
            queryWrapper.eq(AccountDO::getHid, hid);
            return baseMapper.selectOne(queryWrapper);
        } finally {
            // 需要重新初始化用户上下文
            // 这种方式容易出错，不推荐使用
        }
    }
}
```

## 🎯 最佳实践

### 1. 注解使用原则

- `@InterceptorIgnore` **只能在Mapper层使用**
- 支持的参数：
  - `tenantLine = "true"` - 忽略租户隔离
  - `dataPermission = "true"` - 忽略数据权限
  - `blockAttack = "true"` - 忽略防全表更新与删除

### 2. 方法命名规范

```java
// 建议的命名规范
public interface WebAccountMapper extends BaseMapper<AccountDO> {
    
    // 普通查询（会被租户隔离）
    AccountDO selectByPhoneNumber(String phoneNumber);
    
    // 忽略租户隔离的查询（明确标识）
    @InterceptorIgnore(tenantLine = "true")
    AccountDO selectByPhoneNumberIgnoreTenant(String phoneNumber);
    
    // 跨租户查询（明确标识）
    @InterceptorIgnore(tenantLine = "true")
    List<AccountDO> selectAllHospitalsIgnoreTenant();
}
```

### 3. 使用场景

**适合使用 `@InterceptorIgnore` 的场景**：

1. **登录验证**: 根据手机号和院区ID查询用户
2. **跨租户统计**: 需要统计所有租户的数据
3. **系统管理**: 超级管理员查看所有租户数据
4. **数据迁移**: 批量处理多租户数据

**不适合使用的场景**：

1. **普通业务查询**: 应该遵循租户隔离原则
2. **用户数据操作**: 用户只能操作自己租户的数据

## 🔧 验证方法

### 1. 查看生成的SQL

启用SQL日志查看实际执行的SQL：

```yaml
# application.yaml
logging:
  level:
    com.baomidou.mybatisplus: DEBUG
```

**期望结果**：
```sql
-- 使用 @InterceptorIgnore 的方法
SELECT * FROM t_b_account WHERE phone_number = ? AND hid = ? AND deleted = 0

-- 普通方法（会被租户插件拦截）
SELECT * FROM t_b_account WHERE phone_number = ? AND hid = ? AND deleted = 0 AND hid = ?
```

### 2. 单元测试验证

```java
@Test
public void testIgnoreTenantQuery() {
    // 设置租户上下文
    UserContext.set(UserContext.builder().hospitalId(1L).build());
    
    // 调用忽略租户隔离的方法
    AccountDO account = webAccountMapper.selectByPhoneNumberAndHidIgnoreTenant("***********", 2L);
    
    // 应该能查询到其他租户的数据
    assertNotNull(account);
    assertEquals(2L, account.getHid());
}
```

## 📝 总结

### ✅ 关键要点

1. **`@InterceptorIgnore` 注解只在Mapper层生效**
2. **Service层的注解无效**
3. **推荐使用自定义Mapper方法解决**
4. **明确标识忽略租户隔离的方法**

### 🚀 修复后的效果

- ✅ `@InterceptorIgnore` 注解正常工作
- ✅ 可以查询指定租户的数据而不受当前租户上下文影响
- ✅ SQL不会被自动添加额外的租户过滤条件
- ✅ 代码结构清晰，易于维护

现在您的 `findByPhoneNumberAndHidIgnoreTenant` 方法应该可以正常工作，不会再出现 `hid = -1` 的问题了！

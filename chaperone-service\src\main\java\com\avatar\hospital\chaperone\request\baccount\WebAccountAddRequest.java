package com.avatar.hospital.chaperone.request.baccount;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public class WebAccountAddRequest implements Serializable {


    /**
     * 院区ID
     */
    private Long hospitalId;


    /**
     * 部门ID
     */
    private Set<Long> departmentIds;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 用户头像地址
     */
    private String avatarUrl;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.AccountStatus
     */
    private Integer status;

    /**
     * 用户类型
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.AccountType
     */
    private Integer type;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 更新者ID
     */
    private Long updateBy;

}

package com.avatar.hospital.chaperone.database.plan.mapper;

import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanDO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 巡检计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PatrolPlanMapper extends BaseMapper<PatrolPlanDO> {

    // 忽略多租户插件，查询所有租户的数据
    @InterceptorIgnore(tenantLine = "true")
    @Select("SELECT * FROM t_project_patrol_plan ${ew.customSqlSegment}")
    List<PatrolPlanDO> selectAllWithoutTenant(@Param("ew") LambdaQueryWrapper<PatrolPlanDO> queryWrapper);

}

package com.avatar.hospital.chaperone.job;

import lombok.Getter;

/**
 * Description:
 * 订单日志事件
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum ScheduledType {
    
    PATROL_PLAN_TASK_DAY("PATROL_PLAN_TASK_DAY", "巡检计划任务创建(每天执行一次)", "scheduled:patrol_plan_task_day"),
    MAINTENANCE_PLAN_TASK_DAY("MAINTENANCE_PLAN_TASK_DAY", "维保计划任务创建(每天执行一次)", "scheduled:maintenance_plan_task_day"),
    ;

    private final String code;

    private final String describe;


    private final String lockKey;


    ScheduledType(String code, String describe, String lockKey) {
        this.code = code;
        this.describe = describe;
        this.lockKey = lockKey;
    }

    @Override
    public String toString() {
        return describe + "(" + code + ")" + ",lockKey:" + lockKey;
    }
}

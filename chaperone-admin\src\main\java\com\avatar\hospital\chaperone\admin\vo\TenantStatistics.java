package com.avatar.hospital.chaperone.admin.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 租户统计信息VO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class TenantStatistics {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 用户统计
     */
    private UserStatistics userStats;

    /**
     * 计划统计
     */
    private PlanStatistics planStats;

    /**
     * 设备统计
     */
    private DeviceStatistics deviceStats;

    /**
     * 订单统计
     */
    private OrderStatistics orderStats;

    /**
     * 系统使用统计
     */
    private SystemUsageStatistics systemStats;

    /**
     * 统计时间
     */
    private LocalDateTime statisticsTime;

    @Data
    public static class UserStatistics {
        /**
         * 总用户数
         */
        private Long totalUsers;

        /**
         * 活跃用户数
         */
        private Long activeUsers;

        /**
         * B端用户数
         */
        private Long bUsers;

        /**
         * C端用户数
         */
        private Long cUsers;

        /**
         * 本月新增用户数
         */
        private Long monthlyNewUsers;
    }

    @Data
    public static class PlanStatistics {
        /**
         * 总计划数
         */
        private Long totalPlans;

        /**
         * 巡检计划数
         */
        private Long patrolPlans;

        /**
         * 维护计划数
         */
        private Long maintenancePlans;

        /**
         * 生效计划数
         */
        private Long activePlans;

        /**
         * 作废计划数
         */
        private Long inactivePlans;

        /**
         * 本月新增计划数
         */
        private Long monthlyNewPlans;
    }

    @Data
    public static class DeviceStatistics {
        /**
         * 总设备数
         */
        private Long totalDevices;

        /**
         * 正常设备数
         */
        private Long normalDevices;

        /**
         * 故障设备数
         */
        private Long faultyDevices;

        /**
         * 维护中设备数
         */
        private Long maintenanceDevices;

        /**
         * 本月新增设备数
         */
        private Long monthlyNewDevices;
    }

    @Data
    public static class OrderStatistics {
        /**
         * 总订单数
         */
        private Long totalOrders;

        /**
         * 已完成订单数
         */
        private Long completedOrders;

        /**
         * 进行中订单数
         */
        private Long processingOrders;

        /**
         * 已取消订单数
         */
        private Long cancelledOrders;

        /**
         * 总订单金额
         */
        private BigDecimal totalAmount;

        /**
         * 本月订单数
         */
        private Long monthlyOrders;

        /**
         * 本月订单金额
         */
        private BigDecimal monthlyAmount;
    }

    @Data
    public static class SystemUsageStatistics {
        /**
         * 存储使用量（MB）
         */
        private Long storageUsed;

        /**
         * 存储限制（MB）
         */
        private Long storageLimit;

        /**
         * API调用次数（本月）
         */
        private Long monthlyApiCalls;

        /**
         * 最后登录时间
         */
        private LocalDateTime lastLoginTime;

        /**
         * 活跃天数（本月）
         */
        private Integer monthlyActiveDays;
    }

}

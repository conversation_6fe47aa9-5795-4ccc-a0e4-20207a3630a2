package com.avatar.hospital.chaperone.response.hospital;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * 院区分页查询响应
 */
@Data
public class HospitalResourceDataResponse implements Serializable {

    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 院区名称
     */
    private String name;

}

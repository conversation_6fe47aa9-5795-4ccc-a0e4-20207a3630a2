package com.avatar.hospital.chaperone.tenant;

import com.avatar.hospital.chaperone.context.UserContext;

/**
 * 租户上下文兼容类
 * 委托给UserContext实现，保持API兼容性
 * 这样可以避免重复的ThreadLocal管理，统一使用UserContext
 */
public class TenantContext {
    
    /**
     * 设置当前院区ID
     */
    public static void setHospitalId(Long hospitalId) {
        UserContext.setHospitalId(hospitalId);
    }
    
    /**
     * 获取当前院区ID
     */
    public static Long getHospitalId() {
        return UserContext.getHospitalId();
    }
    
    /**
     * 清除租户上下文
     */
    public static void clear() {
        UserContext.clear();
    }
    
    /**
     * 检查是否设置了院区ID
     */
    public static boolean hasHospitalId() {
        return UserContext.hasHospitalId();
    }
    
    // 兼容性方法，保持与原有代码的兼容
    public static void setTenantId(Long tenantId) {
        UserContext.setTenantId(tenantId);
    }
    
    public static Long getTenantId() {
        return UserContext.getTenantId();
    }
    
    public static boolean hasTenantId() {
        return UserContext.hasTenantId();
    }
}

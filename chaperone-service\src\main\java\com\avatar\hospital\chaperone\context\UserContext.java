package com.avatar.hospital.chaperone.context;

import lombok.Builder;
import lombok.Data;

/**
 * 统一的用户上下文 - 支持用户信息和租户信息
 * 提供简单的静态方法，任何地方都可以轻松获取用户信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
public class UserContext {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 院区ID - 租户信息
     */
    private Long hospitalId;

    /**
     * 是否管理员
     */
    private boolean isAdmin;

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String email;

    private static final ThreadLocal<UserContext> CONTEXT = new ThreadLocal<>();

    // === 基础上下文管理 ===

    /**
     * 设置用户上下文
     */
    public static void set(UserContext context) {
        CONTEXT.set(context);
    }

    /**
     * 获取用户上下文
     */
    public static UserContext get() {
        return CONTEXT.get();
    }

    /**
     * 清除用户上下文
     */
    public static void clear() {
        CONTEXT.remove();
    }

    /**
     * 检查是否存在用户上下文
     */
    public static boolean exists() {
        return CONTEXT.get() != null;
    }

    // ========== 核心功能：简单的静态方法获取用户信息 ==========

    /**
     * 获取当前用户ID
     * 使用示例: Long userId = UserContext.currentUserId();
     */
    public static Long currentUserId() {
        UserContext context = get();
        return context != null ? context.userId : null;
    }

    /**
     * 获取当前院区ID（租户ID）- 核心功能1
     * 使用示例: Long hospitalId = UserContext.currentHospitalId();
     */
    public static Long currentHospitalId() {
        UserContext context = get();
        return context != null ? context.hospitalId : 0;
    }

    /**
     * 获取当前用户昵称
     * 使用示例: String nickname = UserContext.currentNickname();
     */
    public static String currentNickname() {
        UserContext context = get();
        return context != null ? context.nickname : null;
    }

    /**
     * 获取当前用户名
     * 使用示例: String username = UserContext.currentUsername();
     */
    public static String currentUsername() {
        UserContext context = get();
        return context != null ? context.username : null;
    }

    /**
     * 获取当前用户手机号
     * 使用示例: String phone = UserContext.currentPhoneNumber();
     */
    public static String currentPhoneNumber() {
        UserContext context = get();
        return context != null ? context.phoneNumber : null;
    }

    /**
     * 检查当前用户是否为管理员
     * 使用示例: boolean isAdmin = UserContext.isCurrentAdmin();
     */
    public static boolean isCurrentAdmin() {
        UserContext context = get();
        return context != null && context.isAdmin;
    }

    /**
     * 检查是否有院区ID
     */
    public static boolean hasHospitalId() {
        return currentHospitalId() != null;
    }

    // ========== 兼容方法（保持现有代码正常工作） ==========

    public static Long getCurrentUserId() {
        return currentUserId();
    }

    public static Long getCurrentHospitalId() {
        return currentHospitalId();
    }

    public static String getCurrentNickname() {
        return currentNickname();
    }

    public static String getCurrentUsername() {
        return currentUsername();
    }

    // TenantContext兼容
    public static Long getTenantId() {
        return currentHospitalId();
    }

    public static Long getHospitalId() {
        return currentHospitalId();
    }

    public static boolean hasTenantId() {
        return hasHospitalId();
    }

    /**
     * 设置院区ID（用于动态修改）
     */
    public static void setHospitalId(Long hospitalId) {
        UserContext context = get();
        if (context != null) {
            context.hospitalId = hospitalId;
        }
    }

    /**
     * 设置租户ID（兼容TenantContext）
     */
    public static void setTenantId(Long tenantId) {
        setHospitalId(tenantId);
    }
}

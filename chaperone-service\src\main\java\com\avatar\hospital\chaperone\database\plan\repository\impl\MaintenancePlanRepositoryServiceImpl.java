package com.avatar.hospital.chaperone.database.plan.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanStatusType;
import com.avatar.hospital.chaperone.database.plan.mapper.MaintenancePlanMapper;
import com.avatar.hospital.chaperone.database.plan.repository.MaintenancePlanRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 维保计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class MaintenancePlanRepositoryServiceImpl extends ServiceImpl<MaintenancePlanMapper, MaintenancePlanDO> implements MaintenancePlanRepositoryService {

    @Override
    public boolean update(PlanRequest request) {
        LambdaUpdateWrapper<MaintenancePlanDO> update = updateWrapper();
        update.eq(PlanDO::getId, request.getId());
        update.set(Objects.nonNull(request.getName()), PlanDO::getName, request.getName());
        update.set(Objects.nonNull(request.getCircleType()), PlanDO::getCircleType, request.getCircleType());
        update.set(Objects.nonNull(request.getCircle()), PlanDO::getCircle, request.getCircle());
        update.set(Objects.nonNull(request.getRemark()), PlanDO::getRemark, request.getRemark());

        update.set(PlanDO::getUpdateBy, request.getOperator());
        update.set(PlanDO::getUpdatedAt, LocalDateTime.now());
        return this.update(update);
    }

    @Override
    public boolean abandon(PlanRequest request) {
        LambdaUpdateWrapper<MaintenancePlanDO> update = updateWrapper();
        update.eq(PlanDO::getId, request.getId());

        update.set(PlanDO::getStatus, PlanStatusType.ABANDON.getCode());
        update.set(PlanDO::getUpdateBy, request.getOperator());
        update.set(PlanDO::getUpdatedAt, LocalDateTime.now());
        return this.update(update);
    }

    @Override
    public LambdaQueryWrapper<MaintenancePlanDO> queryWrapper() {
        LambdaQueryWrapper<MaintenancePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaintenancePlanDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    @Override
    public List<MaintenancePlanDO> selectAllWithoutTenant(LambdaQueryWrapper<MaintenancePlanDO> queryWrapper) {
        return baseMapper.selectAllWithoutTenant(queryWrapper);
    }

    private LambdaUpdateWrapper<MaintenancePlanDO> updateWrapper() {
        LambdaUpdateWrapper<MaintenancePlanDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MaintenancePlanDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}

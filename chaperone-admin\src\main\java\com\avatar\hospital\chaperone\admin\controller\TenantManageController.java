package com.avatar.hospital.chaperone.admin.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckRole;
import com.avatar.hospital.chaperone.admin.common.Result;
import com.avatar.hospital.chaperone.admin.service.TenantManageService;
import com.avatar.hospital.chaperone.admin.vo.TenantInfo;
import com.avatar.hospital.chaperone.admin.vo.TenantStatistics;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 租户管理控制器
 * 超级管理员可以管理所有租户的数据
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/tenant")
@SaCheckLogin
@SaCheckRole("SUPER_ADMIN")
public class TenantManageController {

    @Autowired
    private TenantManageService tenantManageService;

    /**
     * 获取所有租户列表
     */
    @GetMapping("/list")
    public Result<Page<TenantInfo>> getTenantList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        
        Page<TenantInfo> page = tenantManageService.getTenantList(current, size, keyword);
        return Result.success(page);
    }

    /**
     * 获取租户详情
     */
    @GetMapping("/{tenantId}")
    public Result<TenantInfo> getTenantDetail(@PathVariable Long tenantId) {
        TenantInfo tenantInfo = tenantManageService.getTenantDetail(tenantId);
        if (tenantInfo == null) {
            return Result.notFound("租户不存在");
        }
        return Result.success(tenantInfo);
    }

    /**
     * 获取租户统计信息
     */
    @GetMapping("/{tenantId}/statistics")
    public Result<TenantStatistics> getTenantStatistics(@PathVariable Long tenantId) {
        TenantStatistics statistics = tenantManageService.getTenantStatistics(tenantId);
        return Result.success(statistics);
    }

    /**
     * 获取所有租户的统计概览
     */
    @GetMapping("/statistics/overview")
    public Result<List<TenantStatistics>> getAllTenantsStatistics() {
        List<TenantStatistics> statistics = tenantManageService.getAllTenantsStatistics();
        return Result.success(statistics);
    }

    /**
     * 切换租户状态（启用/禁用）
     */
    @PostMapping("/{tenantId}/toggle-status")
    public Result<String> toggleTenantStatus(@PathVariable Long tenantId) {
        boolean success = tenantManageService.toggleTenantStatus(tenantId);
        if (success) {
            return Result.success("操作成功");
        } else {
            return Result.error("操作失败");
        }
    }

    /**
     * 重置租户数据（危险操作）
     */
    @PostMapping("/{tenantId}/reset-data")
    @SaCheckRole("SUPER_ADMIN") // 只有超级管理员可以执行
    public Result<String> resetTenantData(@PathVariable Long tenantId,
                                         @RequestParam String confirmCode) {
        // 验证确认码
        if (!"RESET_CONFIRM_2024".equals(confirmCode)) {
            return Result.badRequest("确认码错误");
        }

        boolean success = tenantManageService.resetTenantData(tenantId);
        if (success) {
            return Result.success("租户数据重置成功");
        } else {
            return Result.error("租户数据重置失败");
        }
    }

    /**
     * 导出租户数据
     */
    @GetMapping("/{tenantId}/export")
    public Result<String> exportTenantData(@PathVariable Long tenantId) {
        String exportPath = tenantManageService.exportTenantData(tenantId);
        return Result.success("数据导出成功", exportPath);
    }

}
